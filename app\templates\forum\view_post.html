{% extends "base.html" %}

{% block title %}{{ post.title }} - Community Forum{% endblock %}

{% block content %}
<div class="post-view-container">
    <div class="post-navigation">
        <a href="{{ url_for('main.forum') }}" class="back-btn">
            <i class="fas fa-arrow-left"></i>
            Back to Forum
        </a>
    </div>

    <article class="post-detail">
        <header class="post-header">
            <div class="author-section">
                <div class="author-avatar">
                    {% if author_profile_picture %}
                        <img src="{{ url_for('static', filename='uploads/' + author_profile_picture) }}" alt="{{ post.author_username }}">
                    {% else %}
                        <i class="fas fa-user-circle"></i>
                    {% endif %}
                </div>
                <div class="author-info">
                    <h3>{{ post.author_username }}</h3>
                    <time datetime="{{ post.created_at.isoformat() }}">
                        {{ post.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                    </time>
                    {% if post.updated_at != post.created_at %}
                        <small class="edited-note">
                            <i class="fas fa-edit"></i>
                            Edited {{ post.updated_at.strftime('%B %d, %Y at %I:%M %p') }}
                        </small>
                    {% endif %}
                </div>
            </div>
            
            <div class="post-actions">
                {% if session.user_id == post.author_id|string %}
                    <a href="{{ url_for('main.edit_post', post_id=post._id) }}" class="action-btn edit-btn">
                        <i class="fas fa-edit"></i>
                        Edit
                    </a>
                    <button class="action-btn delete-btn" onclick="confirmDelete('{{ post._id }}')">
                        <i class="fas fa-trash"></i>
                        Delete
                    </button>
                {% endif %}
            </div>
        </header>

        <div class="post-content">
            <h1 class="post-title">{{ post.title }}</h1>
            
            {% if post.image_filename %}
            <div class="post-image">
                <img src="{{ url_for('static', filename='uploads/' + post.image_filename) }}" alt="{{ post.title }}">
            </div>
            {% endif %}
            
            <div class="post-text">
                {{ post.content|nl2br|safe }}
            </div>
        </div>

        <footer class="post-footer">
            <div class="post-stats">
                <div class="stat-item">
                    <i class="fas fa-eye"></i>
                    <span>{{ post.views }} views</span>
                </div>
                <button class="stat-item like-btn {{ 'liked' if current_user_liked else '' }}" data-post-id="{{ post._id }}">
                    <i class="fas fa-heart"></i>
                    <span id="likes-count">{{ post.likes|length }}</span>
                    <span>likes</span>
                </button>
                <div class="stat-item">
                    <i class="fas fa-comment"></i>
                    <span>{{ comments|length }} comments</span>
                </div>
            </div>
        </footer>
    </article>

    <section class="comments-section">
        <div class="comments-header">
            <h2><i class="fas fa-comments"></i> Comments ({{ comments|length }})</h2>
        </div>

        {% if session.user_id %}
        <div class="comment-form-container">
            <form method="POST" class="comment-form">
                {{ comment_form.hidden_tag() }}
                <div class="comment-input-group">
                    <div class="commenter-avatar">
                        {% if current_user_profile_picture %}
                            <img src="{{ url_for('static', filename='uploads/' + current_user_profile_picture) }}" alt="{{ session.username }}">
                        {% else %}
                            <i class="fas fa-user-circle"></i>
                        {% endif %}
                    </div>
                    <div class="comment-input-wrapper">
                        {{ comment_form.content(class="comment-input", placeholder="Add a comment...") }}
                        <div class="comment-actions">
                            {{ comment_form.submit(class="btn btn-primary btn-sm") }}
                        </div>
                    </div>
                </div>
                {% for error in comment_form.content.errors %}
                    <span class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        {{ error }}
                    </span>
                {% endfor %}
            </form>
        </div>
        {% else %}
        <div class="login-prompt">
            <p><a href="{{ url_for('main.login') }}">Login</a> to join the conversation</p>
        </div>
        {% endif %}

        <div class="comments-list">
            {% for comment in comments %}
            <div class="comment" data-comment-id="{{ comment._id }}">
                <div class="comment-avatar">
                    {% if comment.author_profile_picture %}
                        <img src="{{ url_for('static', filename='uploads/' + comment.author_profile_picture) }}" alt="{{ comment.author_username }}">
                    {% else %}
                        <i class="fas fa-user-circle"></i>
                    {% endif %}
                </div>
                <div class="comment-content">
                    <div class="comment-header">
                        <h4 class="comment-author">{{ comment.author_username }}</h4>
                        <time class="comment-time" datetime="{{ comment.created_at.isoformat() }}">
                            {{ comment.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                        </time>
                        {% if session.user_id == comment.author_id|string %}
                        <div class="comment-actions">
                            <button class="comment-action-btn" onclick="editComment('{{ comment._id }}')">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="comment-action-btn delete" onclick="deleteComment('{{ comment._id }}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                        {% endif %}
                    </div>
                    <div class="comment-text" id="comment-text-{{ comment._id }}">
                        {{ comment.content|nl2br|safe }}
                    </div>
                    {% if comment.updated_at != comment.created_at %}
                        <small class="comment-edited">
                            <i class="fas fa-edit"></i>
                            Edited {{ comment.updated_at.strftime('%B %d, %Y at %I:%M %p') }}
                        </small>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
            
            {% if not comments %}
            <div class="no-comments">
                <i class="fas fa-comment-slash"></i>
                <p>No comments yet. Be the first to share your thoughts!</p>
            </div>
            {% endif %}
        </div>
    </section>
</div>

<script>
// Like functionality
document.querySelector('.like-btn').addEventListener('click', async function() {
    const postId = this.dataset.postId;
    try {
        const response = await fetch(`/like_post/${postId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });
        const data = await response.json();
        if (data.success) {
            document.getElementById('likes-count').textContent = data.likes_count;
            this.classList.toggle('liked', data.liked);
        }
    } catch (error) {
        console.error('Error:', error);
    }
});

// Delete post confirmation
function confirmDelete(postId) {
    if (confirm('Are you sure you want to delete this post? This action cannot be undone.')) {
        window.location.href = `/delete_post/${postId}`;
    }
}

// Comment functionality
function editComment(commentId) {
    // Implementation for editing comments
    console.log('Edit comment:', commentId);
}

function deleteComment(commentId) {
    if (confirm('Are you sure you want to delete this comment?')) {
        // Implementation for deleting comments
        console.log('Delete comment:', commentId);
    }
}

// Auto-resize comment textarea
document.querySelector('.comment-input').addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
});
</script>
{% endblock %}
