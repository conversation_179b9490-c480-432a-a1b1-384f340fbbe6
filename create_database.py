#!/usr/bin/env python3
"""
Database creation script for student_app
This script creates the MongoDB database with all necessary collections and indexes
"""

from pymongo import MongoClient
from werkzeug.security import generate_password_hash
import sys

def create_student_app_database():
    """Create the student_app database with collections and indexes"""
    try:
        # Connect to MongoDB
        client = MongoClient('mongodb://localhost:27017/')
        
        # Create/access the student_app database
        db = client['student_app']
        
        print("Creating student_app database...")
        
        # Force database creation by inserting and removing a dummy document
        dummy_doc = {"_temp": "temp"}
        result = db.temp_collection.insert_one(dummy_doc)
        db.temp_collection.delete_one({"_id": result.inserted_id})
        # Clean up the temporary collection
        db.drop_collection("temp_collection")
        
        print("✓ Database created successfully")
        
        # Create users collection with unique index on username
        print("Creating users collection...")
        db.users.create_index("username", unique=True)
        print("✓ Users collection created with unique username index")
        
        # Create indexes for posts collection
        print("Creating posts collection...")
        db.posts.create_index([("created_at", -1)])  # For sorting by date
        db.posts.create_index([("author_id", 1)])    # For user's posts
        db.posts.create_index([("views", -1)])       # For sorting by popularity
        print("✓ Posts collection created with indexes")
        
        # Create indexes for comments collection
        print("Creating comments collection...")
        db.comments.create_index([("post_id", 1)])   # For post comments
        db.comments.create_index([("author_id", 1)]) # For user's comments
        db.comments.create_index([("created_at", 1)]) # For sorting comments
        print("✓ Comments collection created with indexes")
        
        # Create indexes for blogs collection
        print("Creating blogs collection...")
        db.blogs.create_index([("created_at", -1)])  # For sorting by date
        db.blogs.create_index([("author_id", 1)])    # For user's blogs
        db.blogs.create_index([("status", 1)])       # For filtering by status
        db.blogs.create_index([("tags", 1)])         # For tag-based searches
        print("✓ Blogs collection created with indexes")
        
        # Create a test user if no users exist
        if db.users.count_documents({}) == 0:
            print("Creating test user...")
            test_user = {
                "username": "testuser",
                "password": generate_password_hash("testpass123", method='sha256')
            }
            db.users.insert_one(test_user)
            print("✓ Test user created: testuser / testpass123")
        else:
            print("✓ Users already exist in database")
        
        # Verify database exists
        db_list = client.list_database_names()
        
        print("\n" + "="*50)
        print("DATABASE SETUP COMPLETE!")
        print("="*50)
        print(f"Database name: {db.name}")
        print(f"Database exists: {'student_app' in db_list}")
        print(f"Collections created: {db.list_collection_names()}")
        print(f"MongoDB connection: {client.address}")
        print("\nYou can now run your Flask application!")
        print("="*50)
        
        # Close connection
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating database: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Student App Database Creator")
    print("="*30)
    
    success = create_student_app_database()
    
    if success:
        print("\n✅ Database setup completed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Database setup failed!")
        sys.exit(1)
