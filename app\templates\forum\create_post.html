{% extends "base.html" %}

{% block title %}{{ 'Edit Post' if editing else 'Create New Post' }} - Secure Flask App{% endblock %}

{% block content %}
<div class="create-post-container">
    <div class="create-post-header">
        <h1>
            <i class="fas fa-{{ 'edit' if editing else 'plus-circle' }}"></i>
            {{ 'Edit Post' if editing else 'Create New Post' }}
        </h1>
        <p>{{ 'Update your post content and settings' if editing else 'Share your thoughts, images, and connect with the community' }}</p>
    </div>

    <div class="create-post-form">
        <form method="POST" enctype="multipart/form-data" class="post-form">
            {{ form.hidden_tag() }}
            
            <div class="form-group">
                <label for="{{ form.title.id }}" class="form-label">
                    <i class="fas fa-heading"></i>
                    {{ form.title.label.text }}
                </label>
                {{ form.title(class="form-control", placeholder="Enter an engaging title for your post...") }}
                {% for error in form.title.errors %}
                    <span class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        {{ error }}
                    </span>
                {% endfor %}
            </div>

            <div class="form-group">
                <label for="{{ form.content.id }}" class="form-label">
                    <i class="fas fa-edit"></i>
                    {{ form.content.label.text }}
                </label>
                {{ form.content(class="form-control content-textarea", placeholder="Share your thoughts, experiences, or ask questions...") }}
                <div class="character-count">
                    <span id="char-count">0</span> / 5000 characters
                </div>
                {% for error in form.content.errors %}
                    <span class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        {{ error }}
                    </span>
                {% endfor %}
            </div>

            <div class="form-group">
                <label for="{{ form.image.id }}" class="form-label">
                    <i class="fas fa-image"></i>
                    {{ form.image.label.text }}
                </label>
                <div class="file-upload-area" id="file-upload-area">
                    {{ form.image(class="file-input", id="file-input", accept="image/*") }}
                    <div class="upload-placeholder">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <p>Click to upload an image or drag and drop</p>
                        <small>Supports: JPG, PNG, GIF, WebP (Max 16MB)</small>
                    </div>
                    <div class="image-preview" id="image-preview" style="display: none;">
                        <img id="preview-img" src="" alt="Preview">
                        <button type="button" class="remove-image" id="remove-image">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                {% for error in form.image.errors %}
                    <span class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        {{ error }}
                    </span>
                {% endfor %}
            </div>

            <div class="form-actions">
                {% if editing %}
                    <a href="{{ url_for('main.view_post', post_id=post._id) }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Back to Post
                    </a>
                {% else %}
                    <a href="{{ url_for('main.forum') }}" class="btn btn-secondary">
                        <i class="fas fa-times"></i>
                        Cancel
                    </a>
                {% endif %}
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-{{ 'save' if editing else 'plus' }}"></i>
                    {{ 'Update Post' if editing else 'Create Post' }}
                </button>
            </div>
        </form>
    </div>

    <div class="post-tips">
        <h3><i class="fas fa-lightbulb"></i> Tips for Great Posts</h3>
        <ul>
            <li><i class="fas fa-check"></i> Use a clear, descriptive title</li>
            <li><i class="fas fa-check"></i> Write engaging, well-structured content</li>
            <li><i class="fas fa-check"></i> Add relevant images to enhance your post</li>
            <li><i class="fas fa-check"></i> Be respectful and constructive</li>
            <li><i class="fas fa-check"></i> Proofread before posting</li>
        </ul>
    </div>
</div>

<script>
// Character counter
const contentTextarea = document.querySelector('.content-textarea');
const charCount = document.getElementById('char-count');

contentTextarea.addEventListener('input', function() {
    const count = this.value.length;
    charCount.textContent = count;
    
    if (count > 4500) {
        charCount.style.color = '#dc3545';
    } else if (count > 4000) {
        charCount.style.color = '#ffc107';
    } else {
        charCount.style.color = '#28a745';
    }
});

// File upload handling
const fileInput = document.getElementById('file-input');
const fileUploadArea = document.getElementById('file-upload-area');
const imagePreview = document.getElementById('image-preview');
const previewImg = document.getElementById('preview-img');
const removeImageBtn = document.getElementById('remove-image');
const uploadPlaceholder = document.querySelector('.upload-placeholder');

fileInput.addEventListener('change', handleFileSelect);
removeImageBtn.addEventListener('click', removeImage);

// Drag and drop functionality
fileUploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    fileUploadArea.classList.add('drag-over');
});

fileUploadArea.addEventListener('dragleave', (e) => {
    e.preventDefault();
    fileUploadArea.classList.remove('drag-over');
});

fileUploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    fileUploadArea.classList.remove('drag-over');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        fileInput.files = files;
        handleFileSelect();
    }
});

function handleFileSelect() {
    const file = fileInput.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            uploadPlaceholder.style.display = 'none';
            imagePreview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
}

function removeImage() {
    fileInput.value = '';
    previewImg.src = '';
    uploadPlaceholder.style.display = 'block';
    imagePreview.style.display = 'none';
}

// Auto-resize textarea
contentTextarea.addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = this.scrollHeight + 'px';
});
</script>
{% endblock %}
