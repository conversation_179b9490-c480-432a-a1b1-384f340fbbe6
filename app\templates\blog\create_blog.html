{% extends "base.html" %}

{% block title %}Share Your Story - Secure Flask App{% endblock %}

{% block content %}
<div class="blog-submission-container">
    <div class="blog-header">
        <div class="header-content">
            <h1><i class="fas fa-pen-fancy"></i> Share Your Story</h1>
            <p class="subtitle">We'd love to hear about your personal experiences, insights, and journey. Your story could inspire others!</p>
        </div>
        <div class="header-illustration">
            <i class="fas fa-heart"></i>
        </div>
    </div>

    <div class="blog-form-container">
        <form method="POST" enctype="multipart/form-data" class="blog-form" id="blog-form">
            {{ form.hidden_tag() }}
            
            <!-- Blog Title -->
            <div class="form-group">
                <label for="{{ form.title.id }}" class="form-label">
                    <i class="fas fa-heading"></i>
                    {{ form.title.label.text }}
                </label>
                {{ form.title(class="form-control", placeholder="Give your story a compelling title...") }}
                {% for error in form.title.errors %}
                    <span class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        {{ error }}
                    </span>
                {% endfor %}
            </div>

            <!-- Author Name -->
            <div class="form-group">
                <label for="{{ form.author_name.id }}" class="form-label">
                    <i class="fas fa-user-edit"></i>
                    {{ form.author_name.label.text }}
                </label>
                {{ form.author_name(class="form-control", placeholder="How would you like to be credited? (Leave blank to use your username)") }}
                {% for error in form.author_name.errors %}
                    <span class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        {{ error }}
                    </span>
                {% endfor %}
            </div>

            <!-- Story Content -->
            <div class="form-group">
                <label for="{{ form.content.id }}" class="form-label">
                    <i class="fas fa-feather-alt"></i>
                    {{ form.content.label.text }}
                    <span class="required">*</span>
                </label>
                <div class="textarea-container">
                    {{ form.content(class="form-control story-textarea", placeholder="Share your experience, journey, lessons learned, or insights. Be authentic and let your voice shine through. What happened? How did it affect you? What did you learn? (Minimum 100 words)") }}
                    <div class="character-count">
                        <span id="char-count">0</span> / 10,000 characters
                        <span class="word-count">(<span id="word-count">0</span> words)</span>
                    </div>
                </div>
                {% for error in form.content.errors %}
                    <span class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        {{ error }}
                    </span>
                {% endfor %}
            </div>

            <!-- Image Upload -->
            <div class="form-group">
                <label for="{{ form.image.id }}" class="form-label">
                    <i class="fas fa-image"></i>
                    {{ form.image.label.text }}
                </label>
                <div class="file-upload-area" id="file-upload-area">
                    {{ form.image(class="file-input", id="file-input", accept="image/*") }}
                    <div class="upload-placeholder">
                        <i class="fas fa-cloud-upload-alt"></i>
                        <p>Add a photo to make your story more engaging</p>
                        <small>Click to upload or drag and drop • JPG, PNG, GIF, WebP (Max 16MB)</small>
                    </div>
                    <div class="image-preview" id="image-preview" style="display: none;">
                        <img id="preview-img" src="" alt="Preview">
                        <button type="button" class="remove-image" id="remove-image">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
                {% for error in form.image.errors %}
                    <span class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        {{ error }}
                    </span>
                {% endfor %}
            </div>

            <!-- Tags -->
            <div class="form-group">
                <label for="{{ form.tags.id }}" class="form-label">
                    <i class="fas fa-tags"></i>
                    {{ form.tags.label.text }}
                </label>
                {{ form.tags(class="form-control", placeholder="Add relevant tags (e.g., health, travel, motivation, personal-growth)") }}
                <div class="tag-suggestions">
                    <span class="suggestion-label">Popular tags:</span>
                    <div class="tag-buttons">
                        <button type="button" class="tag-btn" data-tag="health">#health</button>
                        <button type="button" class="tag-btn" data-tag="travel">#travel</button>
                        <button type="button" class="tag-btn" data-tag="motivation">#motivation</button>
                        <button type="button" class="tag-btn" data-tag="personal-growth">#personal-growth</button>
                        <button type="button" class="tag-btn" data-tag="career">#career</button>
                        <button type="button" class="tag-btn" data-tag="relationships">#relationships</button>
                        <button type="button" class="tag-btn" data-tag="lifestyle">#lifestyle</button>
                        <button type="button" class="tag-btn" data-tag="inspiration">#inspiration</button>
                    </div>
                </div>
                {% for error in form.tags.errors %}
                    <span class="error-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        {{ error }}
                    </span>
                {% endfor %}
            </div>

            <!-- Submit Button -->
            <div class="form-actions">
                <a href="{{ url_for('main.forum') }}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i>
                    Back to Community
                </a>
                <button type="submit" class="btn btn-primary" id="submit-btn">
                    <i class="fas fa-rocket"></i>
                    Publish My Story
                </button>
            </div>
        </form>

        <!-- Publishing Notice -->
        <div class="review-notice">
            <div class="notice-icon">
                <i class="fas fa-rocket"></i>
            </div>
            <div class="notice-content">
                <h4>Instant Publishing</h4>
                <p>Your story will be published immediately and visible to all community members. Share your experiences and connect with others!</p>
            </div>
        </div>
    </div>

    <!-- Writing Tips -->
    <div class="writing-tips">
        <h3><i class="fas fa-lightbulb"></i> Writing Tips</h3>
        <div class="tips-grid">
            <div class="tip">
                <i class="fas fa-heart"></i>
                <h4>Be Authentic</h4>
                <p>Share your genuine experiences and emotions</p>
            </div>
            <div class="tip">
                <i class="fas fa-users"></i>
                <h4>Think of Your Audience</h4>
                <p>Write as if you're talking to a friend</p>
            </div>
            <div class="tip">
                <i class="fas fa-book-open"></i>
                <h4>Tell a Story</h4>
                <p>Include beginning, middle, and end with lessons learned</p>
            </div>
            <div class="tip">
                <i class="fas fa-star"></i>
                <h4>Add Details</h4>
                <p>Use specific examples and vivid descriptions</p>
            </div>
        </div>
    </div>
</div>

<script>
// Character and word counter
const contentTextarea = document.querySelector('.story-textarea');
const charCount = document.getElementById('char-count');
const wordCount = document.getElementById('word-count');
const submitBtn = document.getElementById('submit-btn');

function updateCounts() {
    const text = contentTextarea.value;
    const charLength = text.length;
    const wordLength = text.trim() ? text.trim().split(/\s+/).length : 0;
    
    charCount.textContent = charLength;
    wordCount.textContent = wordLength;
    
    // Update character count color
    if (charLength > 9000) {
        charCount.style.color = '#dc3545';
    } else if (charLength > 8000) {
        charCount.style.color = '#ffc107';
    } else {
        charCount.style.color = '#28a745';
    }
    
    // Update submit button state
    if (wordLength < 100) {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> Need at least 100 words';
        submitBtn.classList.add('disabled');
    } else {
        submitBtn.disabled = false;
        submitBtn.innerHTML = '<i class="fas fa-paper-plane"></i> Submit My Story';
        submitBtn.classList.remove('disabled');
    }
}

contentTextarea.addEventListener('input', updateCounts);

// Auto-resize textarea
contentTextarea.addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = Math.max(200, this.scrollHeight) + 'px';
});

// Tag suggestions
document.querySelectorAll('.tag-btn').forEach(btn => {
    btn.addEventListener('click', function() {
        const tag = this.dataset.tag;
        const tagsInput = document.getElementById('tags');
        const currentTags = tagsInput.value;
        
        if (!currentTags.includes(tag)) {
            const newValue = currentTags ? `${currentTags}, ${tag}` : tag;
            tagsInput.value = newValue;
        }
    });
});

// File upload handling (reuse from create_post.html)
const fileInput = document.getElementById('file-input');
const fileUploadArea = document.getElementById('file-upload-area');
const imagePreview = document.getElementById('image-preview');
const previewImg = document.getElementById('preview-img');
const removeImageBtn = document.getElementById('remove-image');
const uploadPlaceholder = document.querySelector('.upload-placeholder');

fileInput.addEventListener('change', handleFileSelect);
removeImageBtn.addEventListener('click', removeImage);

// Drag and drop functionality
fileUploadArea.addEventListener('dragover', (e) => {
    e.preventDefault();
    fileUploadArea.classList.add('drag-over');
});

fileUploadArea.addEventListener('dragleave', (e) => {
    e.preventDefault();
    fileUploadArea.classList.remove('drag-over');
});

fileUploadArea.addEventListener('drop', (e) => {
    e.preventDefault();
    fileUploadArea.classList.remove('drag-over');
    const files = e.dataTransfer.files;
    if (files.length > 0) {
        fileInput.files = files;
        handleFileSelect();
    }
});

function handleFileSelect() {
    const file = fileInput.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            previewImg.src = e.target.result;
            uploadPlaceholder.style.display = 'none';
            imagePreview.style.display = 'block';
        };
        reader.readAsDataURL(file);
    }
}

function removeImage() {
    fileInput.value = '';
    previewImg.src = '';
    uploadPlaceholder.style.display = 'block';
    imagePreview.style.display = 'none';
}

// Initialize counts
updateCounts();
</script>
{% endblock %}
