from flask import Blueprint, render_template, redirect, url_for, flash, session, request, jsonify, current_app
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from .forms import RegistrationForm, LoginForm, ProfileForm
from .models import User
from . import mongo
from bson import ObjectId
from pymongo.errors import DuplicateKeyError
from datetime import datetime, timezone
import os
import uuid

bp = Blueprint('main', __name__)

def calculate_profile_completion(user):
    """Calculate profile completion percentage"""
    total_fields = 7
    completed_fields = 0

    # Check basic fields
    if user.get('full_name'): completed_fields += 1
    if user.get('email'): completed_fields += 1

    # Check profile data
    profile_data = user.get('profile_data', {})
    if profile_data.get('bio'): completed_fields += 1
    if profile_data.get('location'): completed_fields += 1
    if profile_data.get('website'): completed_fields += 1

    # Check preferences (always completed if they exist)
    preferences = profile_data.get('preferences', {})
    if preferences: completed_fields += 2

    return int((completed_fields / total_fields) * 100)

@bp.route('/')
def index():
    if 'user_id' in session:
        return redirect(url_for('main.dashboard'))
    return redirect(url_for('main.login'))

@bp.route('/register', methods=['GET', 'POST'])
def register():
    form = RegistrationForm()
    if form.validate_on_submit():
        try:
            # Check if username or email already exists
            existing_user = mongo.db.users.find_one({
                '$or': [
                    {'username': form.username.data},
                    {'email': form.email.data}
                ]
            })
            if existing_user:
                if existing_user['username'] == form.username.data:
                    flash('Username already exists. Please choose a different one.', 'danger')
                else:
                    flash('Email already exists. Please use a different email.', 'danger')
                return render_template('register.html', form=form)

            hashed_password = generate_password_hash(form.password.data, method='scrypt')
            user = User(
                username=form.username.data,
                password=hashed_password,
                email=form.email.data,
                full_name=form.full_name.data
            )
            result = mongo.db.users.insert_one(user.to_dict())
            print(f"User registered successfully! User ID: {result.inserted_id}")
            print(f"User data: {user.to_dict()}")
            flash('Registration successful! Please log in.', 'success')
            return redirect(url_for('main.login'))
        except Exception as e:
            flash('Registration failed. Please try again.', 'danger')
            print(f"Registration error: {e}")
            return render_template('register.html', form=form)
    return render_template('register.html', form=form)

@bp.route('/login', methods=['GET', 'POST'])
def login():
    form = LoginForm()
    if form.validate_on_submit():
        user = mongo.db.users.find_one({'username': form.username.data})
        print(f"Login attempt for username: {form.username.data}")
        print(f"User found: {user is not None}")
        if user and check_password_hash(user['password'], form.password.data):
            # Update last login time
            mongo.db.users.update_one(
                {'_id': user['_id']},
                {'$set': {'last_login': datetime.now(timezone.utc)}}
            )
            session['user_id'] = str(user['_id'])
            session['username'] = user['username']
            print(f"Login successful for user: {user['username']}")
            flash(f'Welcome back, {user.get("full_name", user["username"])}!', 'success')
            return redirect(url_for('main.dashboard'))
        else:
            print("Login failed: Invalid credentials")
        flash('Login failed. Check your username and/or password.', 'danger')
    return render_template('login.html', form=form)

@bp.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('main.login'))
    user = mongo.db.users.find_one({'_id': ObjectId(session['user_id'])})
    if not user:
        flash('User not found. Please log in again.', 'danger')
        return redirect(url_for('main.login'))

    # Get user statistics
    user_posts_count = mongo.db.posts.count_documents({'author_id': user['_id']})
    user_comments_count = mongo.db.comments.count_documents({'author_id': user['_id']})

    user_stats = {
        'member_since': user.get('created_at', 'Unknown'),
        'last_login': user.get('last_login', 'Never'),
        'total_users': mongo.db.users.count_documents({}),
        'profile_completion': calculate_profile_completion(user),
        'user_posts': user_posts_count,
        'user_comments': user_comments_count,
        'total_posts': mongo.db.posts.count_documents({}),
        'total_comments': mongo.db.comments.count_documents({})
    }

    return render_template('dashboard.html', user=user, stats=user_stats)

@bp.route('/profile', methods=['GET', 'POST'])
def profile():
    if 'user_id' not in session:
        return redirect(url_for('main.login'))

    user = mongo.db.users.find_one({'_id': ObjectId(session['user_id'])})
    if not user:
        flash('User not found. Please log in again.', 'danger')
        return redirect(url_for('main.login'))

    form = ProfileForm()

    if form.validate_on_submit():
        try:
            # Check if email is being changed and if it's already taken
            if form.email.data != user.get('email'):
                existing_email = mongo.db.users.find_one({
                    'email': form.email.data,
                    '_id': {'$ne': user['_id']}
                })
                if existing_email:
                    flash('Email already exists. Please use a different email.', 'danger')
                    return render_template('profile.html', form=form, user=user)

            # Handle profile picture upload
            profile_picture = user.get('profile_data', {}).get('profile_picture', '')
            if form.profile_picture.data:
                new_picture = save_uploaded_file(form.profile_picture.data, current_app.config['UPLOAD_FOLDER'])
                if new_picture:
                    # Delete old profile picture if it exists
                    if profile_picture:
                        old_picture_path = os.path.join(current_app.config['UPLOAD_FOLDER'], profile_picture)
                        if os.path.exists(old_picture_path):
                            os.remove(old_picture_path)
                    profile_picture = new_picture

            # Update user profile
            update_data = {
                'full_name': form.full_name.data,
                'email': form.email.data,
                'profile_data.bio': form.bio.data,
                'profile_data.location': form.location.data,
                'profile_data.website': form.website.data,
                'profile_data.profile_picture': profile_picture,
                'profile_data.preferences.theme': form.theme.data,
                'profile_data.preferences.notifications': form.notifications.data,
                'profile_data.preferences.privacy': form.privacy.data
            }

            mongo.db.users.update_one(
                {'_id': user['_id']},
                {'$set': update_data}
            )

            flash('Profile updated successfully!', 'success')
            return redirect(url_for('main.profile'))

        except Exception as e:
            flash('Profile update failed. Please try again.', 'danger')
            print(f"Profile update error: {e}")

    # Pre-populate form with current user data
    if not form.is_submitted():
        form.full_name.data = user.get('full_name', '')
        form.email.data = user.get('email', '')
        profile_data = user.get('profile_data', {})
        form.bio.data = profile_data.get('bio', '')
        form.location.data = profile_data.get('location', '')
        form.website.data = profile_data.get('website', '')
        preferences = profile_data.get('preferences', {})
        form.theme.data = preferences.get('theme', 'light')
        form.notifications.data = preferences.get('notifications', True)
        form.privacy.data = preferences.get('privacy', 'public')

    return render_template('profile.html', form=form, user=user)

@bp.route('/logout')
def logout():
    username = session.get('username', 'User')
    session.clear()
    flash(f'Goodbye {username}! You have been logged out.', 'success')
    return redirect(url_for('main.login'))

@bp.route('/admin/users')
def admin_users():
    """Simple admin route to view all users (for testing purposes)"""
    try:
        users = list(mongo.db.users.find({}, {'password': 0}))  # Exclude passwords
        total_users = len(users)

        # Convert ObjectId to string for display
        for user in users:
            user['_id'] = str(user['_id'])
            if user.get('created_at'):
                user['created_at'] = user['created_at'].strftime('%Y-%m-%d %H:%M:%S') if hasattr(user['created_at'], 'strftime') else str(user['created_at'])
            if user.get('last_login'):
                user['last_login'] = user['last_login'].strftime('%Y-%m-%d %H:%M:%S') if hasattr(user['last_login'], 'strftime') else str(user['last_login'])

        return f"""
        <html>
        <head>
            <title>Admin - All Users</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 2rem; background: #f5f5f5; }}
                .container {{ background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                h1 {{ color: #333; }}
                .user-card {{ background: #f8f9fa; padding: 1rem; margin: 1rem 0; border-radius: 8px; border-left: 4px solid #667eea; }}
                .user-info {{ margin: 0.5rem 0; }}
                .back-link {{ display: inline-block; margin-top: 1rem; padding: 0.5rem 1rem; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }}
                .stats {{ background: #e3f2fd; padding: 1rem; border-radius: 8px; margin-bottom: 2rem; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🔧 Admin Panel - All Users</h1>
                <div class="stats">
                    <h3>📊 Statistics</h3>
                    <p><strong>Total Users:</strong> {total_users}</p>
                    <p><strong>Database:</strong> secure-flask-app</p>
                    <p><strong>Collection:</strong> users</p>
                </div>

                <h2>👥 User List</h2>
                {''.join([f'''
                <div class="user-card">
                    <div class="user-info"><strong>ID:</strong> {user.get('_id', 'N/A')}</div>
                    <div class="user-info"><strong>Username:</strong> {user.get('username', 'N/A')}</div>
                    <div class="user-info"><strong>Full Name:</strong> {user.get('full_name', 'N/A')}</div>
                    <div class="user-info"><strong>Email:</strong> {user.get('email', 'N/A')}</div>
                    <div class="user-info"><strong>Created:</strong> {user.get('created_at', 'N/A')}</div>
                    <div class="user-info"><strong>Last Login:</strong> {user.get('last_login', 'Never')}</div>
                    <div class="user-info"><strong>Bio:</strong> {user.get('profile_data', {}).get('bio', 'No bio')}</div>
                </div>
                ''' for user in users])}

                <a href="/" class="back-link">← Back to App</a>
            </div>
        </body>
        </html>
        """
    except Exception as e:
        return f"<h1>Error</h1><p>Could not fetch users: {str(e)}</p><a href='/'>Back to App</a>"


@bp.route('/admin/approve-all-blogs')
def approve_all_blogs():
    """Admin route to approve all pending blogs"""
    try:
        # Update all pending blogs to approved
        result = mongo.db.blogs.update_many(
            {'status': 'pending'},
            {'$set': {'status': 'approved'}}
        )

        return f"""
        <html>
        <head>
            <title>Blog Approval Update</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 2rem; background: #f5f5f5; }}
                .container {{ background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                h1 {{ color: #333; }}
                .success {{ background: #d4edda; color: #155724; padding: 1rem; border-radius: 8px; margin: 1rem 0; }}
                .back-link {{ display: inline-block; margin-top: 1rem; padding: 0.5rem 1rem; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🚀 Blog Approval Update</h1>
                <div class="success">
                    <strong>Success!</strong> Updated {result.modified_count} blogs from pending to approved status.
                </div>
                <p>All blogs are now immediately visible to all users.</p>
                <a href="/blogs" class="back-link">View All Blogs</a>
                <a href="/admin/users" class="back-link">Back to Admin</a>
                <a href="/" class="back-link">Back to App</a>
            </div>
        </body>
        </html>
        """
    except Exception as e:
        return f"<h1>Error</h1><p>Could not update blogs: {str(e)}</p><a href='/'>Back to App</a>"


# Helper functions for file uploads
def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']


def save_uploaded_file(file, upload_folder):
    """Save uploaded file and return filename"""
    if file and allowed_file(file.filename):
        # Generate unique filename
        filename = secure_filename(file.filename)
        name, ext = os.path.splitext(filename)
        unique_filename = f"{name}_{uuid.uuid4().hex[:8]}{ext}"

        # Ensure upload directory exists
        os.makedirs(upload_folder, exist_ok=True)

        # Save file
        file_path = os.path.join(upload_folder, unique_filename)
        file.save(file_path)
        return unique_filename
    return None











@bp.route('/chatbot')
def chatbot():
    """AI Assistant Chatbot Interface"""
    if 'user_id' not in session:
        flash('Please log in to access the AI assistant.', 'danger')
        return redirect(url_for('main.login'))

    return render_template('chatbot/index.html')


@bp.route('/chatbot/ask', methods=['POST'])
def chatbot_ask():
    """Handle chatbot questions"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not logged in'})

    data = request.get_json()
    question = data.get('question', '').lower()
    language = data.get('language', 'english')

    # Sample responses for common questions
    responses = {
        'platform': {
            'english': "This is a student platform where you can connect with other students, share experiences through forums and blogs, and get help from our AI assistant.",
            'kannada': "ಇದು ವಿದ್ಯಾರ್ಥಿಗಳ ವೇದಿಕೆಯಾಗಿದ್ದು, ಇಲ್ಲಿ ನೀವು ಇತರ ವಿದ್ಯಾರ್ಥಿಗಳೊಂದಿಗೆ ಸಂಪರ್ಕ ಸಾಧಿಸಬಹುದು, ವೇದಿಕೆ ಮತ್ತು ಬ್ಲಾಗ್‌ಗಳ ಮೂಲಕ ಅನುಭವಗಳನ್ನು ಹಂಚಿಕೊಳ್ಳಬಹುದು.",
            'hindi': "यह एक छात्र मंच है जहाँ आप अन्य छात्रों से जुड़ सकते हैं, फोरम और ब्लॉग के माध्यम से अनुभव साझा कर सकते हैं।",
            'telugu': "ఇది విద్యార్థుల వేదిక, ఇక్కడ మీరు ఇతర విద్యార్థులతో కనెక్ట్ అవ్వగలరు, ఫోరమ్ మరియు బ్లాగుల ద్వారా అనుభవాలను పంచుకోవచ్చు."
        },
        'help': {
            'english': "I can help you navigate the platform, answer questions about using the forum, writing blogs, and connecting with other students.",
            'kannada': "ನಾನು ವೇದಿಕೆಯನ್ನು ನ್ಯಾವಿಗೇಟ್ ಮಾಡಲು, ವೇದಿಕೆಯ ಬಳಕೆ, ಬ್ಲಾಗ್ ಬರೆಯುವುದು ಮತ್ತು ಇತರ ವಿದ್ಯಾರ್ಥಿಗಳೊಂದಿಗೆ ಸಂಪರ್ಕ ಸಾಧಿಸುವ ಬಗ್ಗೆ ಸಹಾಯ ಮಾಡಬಹುದು.",
            'hindi': "मैं प्लेटफॉर्म नेविगेट करने, फोरम का उपयोग करने, ब्लॉग लिखने और अन्य छात्रों से जुड़ने में आपकी मदद कर सकता हूँ।",
            'telugu': "నేను ప్లాట్‌ఫారమ్‌ను నావిగేట్ చేయడంలో, ఫోరమ్ ఉపయోగించడంలో, బ్లాగులు రాయడంలో మరియు ఇతర విద్యార్థులతో కనెక్ట్ అవ్వడంలో సహాయం చేయగలను."
        }
    }

    # Simple keyword matching for demo
    response_text = "I'm here to help you navigate the platform and answer questions. Feel free to ask about using the forum, writing blogs, or connecting with other students."

    for condition, condition_responses in responses.items():
        if condition in question:
            response_text = condition_responses.get(language, condition_responses['english'])
            break

    return jsonify({'response': response_text})












# Footer Pages Routes
@bp.route('/about')
def about():
    """About page"""
    return render_template('footer/about.html')


@bp.route('/privacy')
def privacy():
    """Privacy policy page"""
    return render_template('footer/privacy.html')


@bp.route('/contact')
def contact():
    """Contact page"""
    return render_template('footer/contact.html')


@bp.route('/support')
def support():
    """Support page"""
    return render_template('footer/support.html')