{% extends "base.html" %}

{% block title %}3D Anatomy System - MeduLearn{% endblock %}

{% block content %}
<div class="anatomy-app">
    <!-- Sleek <PERSON> -->
    <header class="anatomy-header">
        <div class="header-left">
            <a href="{{ url_for('main.anatomy_lab') }}" class="back-button">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="m15 18-6-6 6-6"/>
                </svg>
                Back
            </a>
            <div class="system-info">
                <h1>Cardiovascular System</h1>
                <span class="system-subtitle">Interactive 3D Exploration</span>
            </div>
        </div>

        <div class="header-center">
            <div class="mode-selector">
                <button class="mode-tab active" data-mode="explore">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 2v20M2 12h20"/>
                    </svg>
                    Explore
                </button>
                <button class="mode-tab" data-mode="learn">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/>
                        <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/>
                    </svg>
                    Learn
                </button>
                <button class="mode-tab" data-mode="test">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="10"/>
                        <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
                        <path d="M12 17h.01"/>
                    </svg>
                    Test
                </button>
            </div>
        </div>

        <div class="header-right">
            <button class="tool-btn" id="voice-control">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"/>
                    <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
                    <line x1="12" x2="12" y1="19" y2="22"/>
                </svg>
            </button>
            <button class="tool-btn" id="ar-mode">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M2 3h20v18H2z"/>
                    <path d="M8 21v-5a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v5"/>
                </svg>
            </button>
            <button class="tool-btn" id="settings">
                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="3"/>
                    <path d="M12 1v6m0 6v6"/>
                    <path d="m21 12-6 0m-6 0-6 0"/>
                </svg>
            </button>
        </div>
    </header>

    <!-- Main Application Layout -->
    <div class="anatomy-workspace">
        <!-- Left Panel - Tools & Layers -->
        <aside class="left-panel">
            <div class="panel-section">
                <div class="section-header">
                    <h3>Layers</h3>
                    <button class="section-toggle">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="m6 9 6 6 6-6"/>
                        </svg>
                    </button>
                </div>
                <div class="layer-list">
                    <div class="layer-item">
                        <div class="layer-toggle">
                            <input type="checkbox" id="skin" checked>
                            <label for="skin" class="toggle-switch"></label>
                        </div>
                        <div class="layer-info">
                            <span class="layer-name">Skin</span>
                            <div class="layer-controls">
                                <input type="range" class="opacity-slider" min="0" max="100" value="100">
                                <span class="opacity-value">100%</span>
                            </div>
                        </div>
                    </div>

                    <div class="layer-item">
                        <div class="layer-toggle">
                            <input type="checkbox" id="muscle" checked>
                            <label for="muscle" class="toggle-switch"></label>
                        </div>
                        <div class="layer-info">
                            <span class="layer-name">Muscle</span>
                            <div class="layer-controls">
                                <input type="range" class="opacity-slider" min="0" max="100" value="100">
                                <span class="opacity-value">100%</span>
                            </div>
                        </div>
                    </div>

                    <div class="layer-item">
                        <div class="layer-toggle">
                            <input type="checkbox" id="organs" checked>
                            <label for="organs" class="toggle-switch"></label>
                        </div>
                        <div class="layer-info">
                            <span class="layer-name">Organs</span>
                            <div class="layer-controls">
                                <input type="range" class="opacity-slider" min="0" max="100" value="100">
                                <span class="opacity-value">100%</span>
                            </div>
                        </div>
                    </div>

                    <div class="layer-item">
                        <div class="layer-toggle">
                            <input type="checkbox" id="skeleton" checked>
                            <label for="skeleton" class="toggle-switch"></label>
                        </div>
                        <div class="layer-info">
                            <span class="layer-name">Skeleton</span>
                            <div class="layer-controls">
                                <input type="range" class="opacity-slider" min="0" max="100" value="100">
                                <span class="opacity-value">100%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel-section">
                <div class="section-header">
                    <h3>Tools</h3>
                </div>
                <div class="tool-grid">
                    <button class="tool-button" data-tool="rotate">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8"/>
                            <path d="M21 3v5h-5"/>
                        </svg>
                        <span>Rotate</span>
                    </button>
                    <button class="tool-button" data-tool="zoom">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="11" cy="11" r="8"/>
                            <path d="m21 21-4.35-4.35"/>
                            <line x1="11" x2="11" y1="8" y2="14"/>
                            <line x1="8" x2="14" y1="11" y2="11"/>
                        </svg>
                        <span>Zoom</span>
                    </button>
                    <button class="tool-button" data-tool="measure">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 3 9 15l-6-6 12-12z"/>
                            <path d="m5 11 4.5 4.5"/>
                            <path d="m13 5 4.5 4.5"/>
                        </svg>
                        <span>Measure</span>
                    </button>
                    <button class="tool-button" data-tool="section">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 12h18m-9-9v18"/>
                        </svg>
                        <span>Section</span>
                    </button>
                </div>
            </div>
        </aside>

        <!-- Center - 3D Viewer -->
        <main class="viewer-area">
            <div class="viewer-container">
                <div class="model-stage">
                    <div class="model-placeholder">
                        <div class="placeholder-content">
                            <div class="model-icon">
                                <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
                                    <path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2Z"/>
                                    <path d="M12 6v6l4 2"/>
                                </svg>
                            </div>
                            <h2>3D Heart Model</h2>
                            <p>Interactive cardiovascular system with detailed anatomical structures</p>
                            <div class="interaction-hints">
                                <div class="hint">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M3 12h18m-9-9v18"/>
                                    </svg>
                                    <span>Drag to rotate</span>
                                </div>
                                <div class="hint">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="11" cy="11" r="8"/>
                                        <path d="m21 21-4.35-4.35"/>
                                    </svg>
                                    <span>Scroll to zoom</span>
                                </div>
                                <div class="hint">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M9 12l2 2 4-4"/>
                                    </svg>
                                    <span>Click to select</span>
                                </div>
                            </div>
                            <button class="load-button">
                                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <polygon points="5,3 19,12 5,21"/>
                                </svg>
                                Load Model
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Viewer Controls -->
                <div class="viewer-controls">
                    <div class="control-group">
                        <button class="control-btn" data-action="reset">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                                <path d="M21 3v5h-5"/>
                                <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                                <path d="M3 21v-5h5"/>
                            </svg>
                        </button>
                        <button class="control-btn" data-action="screenshot">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"/>
                                <circle cx="12" cy="13" r="4"/>
                            </svg>
                        </button>
                        <button class="control-btn" data-action="fullscreen">
                            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <path d="M8 3H5a2 2 0 0 0-2 2v3m18 0V5a2 2 0 0 0-2-2h-3m0 18h3a2 2 0 0 0 2-2v-3M3 16v3a2 2 0 0 0 2 2h3"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </main>

        <!-- Right Panel - Information -->
        <aside class="right-panel">
            <div class="panel-tabs">
                <button class="tab-button active" data-tab="anatomy">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 2a10 10 0 1 0 10 10A10 10 0 0 0 12 2Z"/>
                        <circle cx="12" cy="12" r="3"/>
                    </svg>
                    Anatomy
                </button>
                <button class="tab-button" data-tab="learn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/>
                        <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/>
                    </svg>
                    Learn
                </button>
                <button class="tab-button" data-tab="ai">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 8V4l3 3-3 3zm0 0v8l3-3-3-3z"/>
                        <circle cx="12" cy="12" r="10"/>
                    </svg>
                    AI
                </button>
            </div>

            <div class="tab-content">
                <div class="tab-panel active" id="anatomy">
                    <div class="search-container">
                        <div class="search-input">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="11" cy="11" r="8"/>
                                <path d="m21 21-4.35-4.35"/>
                            </svg>
                            <input type="text" placeholder="Search structures...">
                        </div>
                    </div>

                    <div class="structure-list">
                        <div class="structure-item" data-structure="heart">
                            <div class="structure-preview">
                                <div class="structure-color" style="background: #e74c3c;"></div>
                                <div class="structure-info">
                                    <h4>Heart</h4>
                                    <p>Muscular pump organ</p>
                                </div>
                            </div>
                            <div class="structure-actions">
                                <button class="action-btn" data-action="focus">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="11" cy="11" r="8"/>
                                        <path d="m21 21-4.35-4.35"/>
                                        <line x1="11" x2="11" y1="8" y2="14"/>
                                        <line x1="8" x2="14" y1="11" y2="11"/>
                                    </svg>
                                </button>
                                <button class="action-btn" data-action="info">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="M12 16v-4"/>
                                        <path d="M12 8h.01"/>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <div class="structure-item" data-structure="aorta">
                            <div class="structure-preview">
                                <div class="structure-color" style="background: #3498db;"></div>
                                <div class="structure-info">
                                    <h4>Aorta</h4>
                                    <p>Main arterial trunk</p>
                                </div>
                            </div>
                            <div class="structure-actions">
                                <button class="action-btn" data-action="focus">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="11" cy="11" r="8"/>
                                        <path d="m21 21-4.35-4.35"/>
                                        <line x1="11" x2="11" y1="8" y2="14"/>
                                        <line x1="8" x2="14" y1="11" y2="11"/>
                                    </svg>
                                </button>
                                <button class="action-btn" data-action="info">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="M12 16v-4"/>
                                        <path d="M12 8h.01"/>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        <div class="structure-item" data-structure="ventricles">
                            <div class="structure-preview">
                                <div class="structure-color" style="background: #9b59b6;"></div>
                                <div class="structure-info">
                                    <h4>Ventricles</h4>
                                    <p>Lower heart chambers</p>
                                </div>
                            </div>
                            <div class="structure-actions">
                                <button class="action-btn" data-action="focus">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="11" cy="11" r="8"/>
                                        <path d="m21 21-4.35-4.35"/>
                                        <line x1="11" x2="11" y1="8" y2="14"/>
                                        <line x1="8" x2="14" y1="11" y2="11"/>
                                    </svg>
                                </button>
                                <button class="action-btn" data-action="info">
                                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="M12 16v-4"/>
                                        <path d="M12 8h.01"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="tab-panel" id="learn">
                    <div class="learning-content">
                        <div class="lesson-card">
                            <div class="lesson-header">
                                <h4>Heart Function</h4>
                                <span class="lesson-duration">5 min</span>
                            </div>
                            <p>Learn how the heart pumps blood through the circulatory system</p>
                            <button class="lesson-btn">Start Lesson</button>
                        </div>

                        <div class="lesson-card">
                            <div class="lesson-header">
                                <h4>Blood Flow</h4>
                                <span class="lesson-duration">8 min</span>
                            </div>
                            <p>Understand the path of blood through the heart chambers</p>
                            <button class="lesson-btn">Start Lesson</button>
                        </div>
                    </div>
                </div>

                <div class="tab-panel" id="ai">
                    <div class="ai-content">
                        <div class="progress-card">
                            <div class="progress-header">
                                <h4>Your Progress</h4>
                                <span class="progress-percentage">78%</span>
                            </div>
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: 78%;"></div>
                            </div>
                            <p>12 of 15 structures mastered</p>
                        </div>

                        <div class="recommendations">
                            <h4>AI Recommendations</h4>
                            <div class="recommendation-item">
                                <div class="rec-icon">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <path d="M9 11H1l8-8 8 8"/>
                                        <path d="M9 11v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V11"/>
                                    </svg>
                                </div>
                                <div class="rec-content">
                                    <h5>Focus on Valves</h5>
                                    <p>Practice identifying heart valves</p>
                                </div>
                            </div>

                            <div class="recommendation-item">
                                <div class="rec-icon">
                                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                        <circle cx="12" cy="12" r="10"/>
                                        <path d="M8 14s1.5 2 4 2 4-2 4-2"/>
                                        <line x1="9" x2="9.01" y1="9" y2="9"/>
                                        <line x1="15" x2="15.01" y1="9" y2="9"/>
                                    </svg>
                                </div>
                                <div class="rec-content">
                                    <h5>Ready for Quiz</h5>
                                    <p>Take the advanced assessment</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </aside>
    </div>

    <!-- Status Bar -->
    <footer class="status-bar">
        <div class="status-section">
            <div class="status-item">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                    <circle cx="12" cy="12" r="3"/>
                </svg>
                <span>Explore Mode</span>
            </div>
            <div class="status-item">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M12 2 2 7l10 5 10-5-10-5z"/>
                    <path d="M2 17l10 5 10-5"/>
                    <path d="M2 12l10 5 10-5"/>
                </svg>
                <span>4 Layers</span>
            </div>
        </div>

        <div class="status-section">
            <div class="session-timer">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <circle cx="12" cy="12" r="10"/>
                    <polyline points="12,6 12,12 16,14"/>
                </svg>
                <span>15:32</span>
            </div>
        </div>

        <div class="status-section">
            <div class="connection-status">
                <div class="status-dot"></div>
                <span>Connected</span>
            </div>
        </div>
    </footer>
</div>

<style>
/* Full Screen Anatomy Explorer Styles */
.anatomy-fullscreen-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 100%);
    display: flex;
    flex-direction: column;
    overflow: hidden;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Modern Navigation Bar */
.anatomy-navbar {
    height: 80px;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(25px);
    border-bottom: 1px solid rgba(148, 163, 184, 0.15);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 3rem;
    position: relative;
    z-index: 1000;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
}

.nav-left {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-back-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1.5rem;
    background: transparent;
    border: 2px solid var(--border-primary);
    border-radius: 12px;
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.95rem;
}

.nav-back-btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.25);
    text-decoration: none;
}

.nav-divider {
    width: 2px;
    height: 32px;
    background: linear-gradient(135deg, var(--border-primary), var(--border-secondary));
    border-radius: 1px;
}

.nav-title {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--text-primary);
    margin: 0;
    letter-spacing: -0.02em;
}

.nav-center {
    flex: 1;
    display: flex;
    justify-content: center;
}

.view-mode-switcher {
    display: flex;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    padding: 4px;
    gap: 4px;
}

.mode-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: transparent;
    border: none;
    border-radius: 8px;
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.mode-btn.active {
    background: white;
    color: var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.mode-btn:hover:not(.active) {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.nav-control-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: transparent;
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    color: var(--text-secondary);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.nav-control-btn:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

/* Main Workspace Layout */
.anatomy-main-workspace {
    flex: 1;
    display: grid;
    grid-template-columns: 320px 1fr 380px;
    gap: 0;
    overflow: hidden;
}

/* Left Sidebar - Controls */
.anatomy-sidebar {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(25px);
    border-right: 1px solid rgba(148, 163, 184, 0.15);
    padding: 2.5rem 2rem;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 3rem;
}

.sidebar-section {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.sidebar-section:hover {
    background: rgba(255, 255, 255, 0.95);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
}

.sidebar-title {
    display: flex;
    align-items: center;
    gap: 1rem;
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 2rem 0;
    letter-spacing: -0.01em;
}

.sidebar-title i {
    color: var(--primary-color);
    font-size: 1.25rem;
}

/* Layer Controls */
.layer-controls {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.layer-item {
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.layer-item:hover {
    border-color: var(--primary-color);
    box-shadow: 0 8px 25px rgba(37, 99, 235, 0.15);
    transform: translateY(-2px);
}

.layer-toggle {
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
    width: 100%;
}

.layer-toggle input[type="checkbox"] {
    display: none;
}

.toggle-slider {
    width: 40px;
    height: 20px;
    background: var(--bg-tertiary);
    border-radius: 10px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 16px;
    height: 16px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.layer-toggle input[type="checkbox"]:checked + .toggle-slider {
    background: var(--primary-color);
}

.layer-toggle input[type="checkbox"]:checked + .toggle-slider::before {
    transform: translateX(20px);
}

.layer-info {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.layer-name {
    font-weight: 500;
    color: var(--text-primary);
}

.layer-opacity {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

/* Control Grid */
.control-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.control-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 48px;
    background: white;
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1.1rem;
}

.control-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Main 3D Viewer */
.anatomy-viewer-main {
    background: linear-gradient(135deg, #fafbfc 0%, #f1f5f9 100%);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem;
}

.viewer-container {
    width: 100%;
    height: 100%;
    position: relative;
    background: radial-gradient(circle at center, rgba(37, 99, 235, 0.03) 0%, transparent 70%);
    border-radius: 24px;
    overflow: hidden;
    box-shadow: inset 0 0 50px rgba(0, 0, 0, 0.05);
}

.viewer-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="30" height="30" patternUnits="userSpaceOnUse"><path d="M 30 0 L 0 0 0 30" fill="none" stroke="rgba(37,99,235,0.08)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
    opacity: 0.4;
}

.model-placeholder {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(30px);
    padding: 5rem 4rem;
    border-radius: 32px;
    border: 1px solid rgba(148, 163, 184, 0.2);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    z-index: 10;
    transition: all 0.3s ease;
}

.model-placeholder:hover {
    transform: translate(-50%, -50%) translateY(-5px);
    box-shadow: 0 30px 80px rgba(0, 0, 0, 0.15);
}

.model-icon i {
    font-size: 8rem;
    margin-bottom: 3rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    filter: drop-shadow(0 4px 8px rgba(37, 99, 235, 0.2));
}

.model-placeholder h2 {
    font-size: 2.5rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
    letter-spacing: -0.02em;
    line-height: 1.2;
}

.model-placeholder p {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 3rem;
    line-height: 1.7;
    max-width: 480px;
    margin-left: auto;
    margin-right: auto;
}

.model-features {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.feature-badge {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    transition: all 0.2s ease;
}

.feature-badge:hover {
    background: var(--bg-tertiary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.feature-badge i {
    color: var(--primary-color);
    font-size: 1.25rem;
}

.feature-badge span {
    font-weight: 500;
    color: var(--text-primary);
}

.load-model-btn {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-md);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin: 0 auto;
}

.load-model-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    background: linear-gradient(135deg, var(--primary-light), var(--secondary-color));
}

/* Floating Controls */
.floating-controls {
    position: absolute;
    bottom: 2rem;
    right: 2rem;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    z-index: 100;
}

.floating-btn {
    width: 56px;
    height: 56px;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-primary);
    border-radius: 50%;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    box-shadow: var(--shadow-lg);
}

.floating-btn:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
}

/* Right Info Panel */
.anatomy-info-panel {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(25px);
    border-left: 1px solid rgba(148, 163, 184, 0.15);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.panel-header {
    padding: 2.5rem 2rem 2rem;
    border-bottom: 1px solid rgba(148, 163, 184, 0.15);
    background: rgba(248, 250, 252, 0.8);
}

.panel-tabs {
    display: flex;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 16px;
    padding: 6px;
    gap: 6px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.panel-tab {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1rem 0.75rem;
    background: transparent;
    border: none;
    border-radius: 12px;
    color: var(--text-secondary);
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.9rem;
}

.panel-tab.active {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
    transform: translateY(-1px);
}

.panel-tab:hover:not(.active) {
    background: rgba(248, 250, 252, 0.8);
    color: var(--text-primary);
    transform: translateY(-1px);
}

.panel-content {
    flex: 1;
    padding: 2.5rem 2rem;
    overflow-y: auto;
}

.tab-pane {
    display: none;
}

.tab-pane.active {
    display: block;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Search Box */
.search-box {
    position: relative;
    margin-bottom: 1.5rem;
}

.search-box i {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-secondary);
}

.search-box input {
    width: 100%;
    padding: 0.75rem 1rem 0.75rem 2.5rem;
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    background: white;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.search-box input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* Structure Cards */
.structures-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.structure-card {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(148, 163, 184, 0.2);
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.structure-card:hover {
    background: rgba(255, 255, 255, 1);
    border-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(37, 99, 235, 0.15);
}

.structure-icon {
    width: 56px;
    height: 56px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    flex-shrink: 0;
    box-shadow: 0 4px 15px rgba(37, 99, 235, 0.3);
}

.structure-details {
    flex: 1;
}

.structure-details h4 {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.75rem 0;
    letter-spacing: -0.01em;
}

.structure-details p {
    font-size: 0.95rem;
    color: var(--text-secondary);
    margin: 0 0 1rem 0;
    line-height: 1.6;
}

.structure-tags {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.tag {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-color);
    padding: 0.4rem 0.8rem;
    border-radius: 8px;
    font-size: 0.8rem;
    font-weight: 600;
}

.structure-action-btn {
    width: 36px;
    height: 36px;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.structure-action-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Procedures Grid */
.procedures-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.procedure-card {
    background: white;
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.2s ease;
}

.procedure-card:hover {
    background: var(--bg-secondary);
    border-color: var(--border-accent);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.procedure-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.procedure-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--secondary-color), var(--success));
    color: white;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.procedure-meta h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
}

.difficulty {
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.difficulty.beginner {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.difficulty.intermediate {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.procedure-card p {
    color: var(--text-secondary);
    margin-bottom: 1rem;
    line-height: 1.5;
}

.procedure-btn {
    background: linear-gradient(135deg, var(--secondary-color), var(--success));
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    justify-content: center;
}

.procedure-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* AI Dashboard */
.ai-dashboard {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.ai-stats {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.stat-card {
    background: white;
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stat-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--info), #06b6d4);
    color: white;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
}

.stat-info {
    flex: 1;
}

.stat-number {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
}

.stat-label {
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.ai-insights {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.insight-item {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    background: white;
    border: 1px solid var(--border-primary);
    border-radius: 12px;
}

.insight-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--warning), #f59e0b);
    color: white;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.insight-text h5 {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
}

.insight-text p {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin: 0;
    line-height: 1.4;
}

/* Bottom Status Bar */
.anatomy-status-bar {
    height: 60px;
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(25px);
    border-top: 1px solid rgba(148, 163, 184, 0.15);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 3rem;
    font-size: 0.9rem;
    color: var(--text-secondary);
    position: relative;
    z-index: 1000;
    box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.05);
}

.status-left,
.status-right {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.status-center {
    flex: 1;
    display: flex;
    justify-content: center;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.status-item i {
    color: var(--primary-color);
}

.progress-indicator {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.progress-bar {
    width: 200px;
    height: 6px;
    background: var(--bg-tertiary);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 3px;
    transition: width 0.3s ease;
}

.progress-text {
    font-weight: 500;
    color: var(--text-primary);
}

/* Responsive Design */
@media (max-width: 1600px) {
    .anatomy-main-workspace {
        grid-template-columns: 300px 1fr 360px;
    }

    .anatomy-sidebar,
    .anatomy-info-panel {
        padding: 2rem 1.5rem;
    }
}

@media (max-width: 1400px) {
    .anatomy-main-workspace {
        grid-template-columns: 280px 1fr 340px;
    }

    .anatomy-sidebar,
    .anatomy-info-panel {
        padding: 1.5rem;
    }

    .sidebar-section {
        padding: 1.5rem;
    }
}

@media (max-width: 1200px) {
    .anatomy-main-workspace {
        grid-template-columns: 260px 1fr 320px;
    }

    .nav-title {
        font-size: 1.3rem;
    }

    .mode-btn span,
    .nav-control-btn span {
        display: none;
    }

    .panel-tab span {
        display: none;
    }

    .anatomy-navbar {
        padding: 0 2rem;
    }
}

@media (max-width: 1024px) {
    .anatomy-main-workspace {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr auto;
        gap: 0;
    }

    .anatomy-sidebar {
        order: 3;
        background: rgba(255, 255, 255, 0.98);
        border-right: none;
        border-top: 1px solid rgba(148, 163, 184, 0.15);
        padding: 2rem;
        overflow-x: auto;
        overflow-y: visible;
        display: flex;
        flex-direction: row;
        gap: 3rem;
        height: auto;
        min-height: 200px;
    }

    .anatomy-info-panel {
        order: 1;
        border-left: none;
        border-bottom: 1px solid rgba(148, 163, 184, 0.15);
        height: 350px;
        min-height: 350px;
    }

    .anatomy-viewer-main {
        order: 2;
        padding: 2rem;
    }

    .sidebar-section {
        min-width: 280px;
        flex-shrink: 0;
    }

    .floating-controls {
        bottom: 1.5rem;
        right: 1.5rem;
    }

    .floating-btn {
        width: 52px;
        height: 52px;
        font-size: 1.2rem;
    }

    .panel-header {
        padding: 2rem;
    }

    .panel-content {
        padding: 2rem;
    }
}

@media (max-width: 768px) {
    .anatomy-navbar {
        padding: 0 1rem;
        height: 60px;
    }

    .nav-left {
        gap: 1rem;
    }

    .nav-back-btn span,
    .nav-title {
        display: none;
    }

    .view-mode-switcher {
        transform: scale(0.9);
    }

    .nav-right {
        gap: 0.25rem;
    }

    .nav-control-btn {
        padding: 0.5rem;
        min-width: 40px;
    }

    .anatomy-sidebar {
        padding: 1rem;
        gap: 1rem;
    }

    .sidebar-section {
        min-width: 200px;
    }

    .anatomy-info-panel {
        height: 250px;
    }

    .panel-header {
        padding: 1rem;
    }

    .panel-content {
        padding: 1rem;
    }

    .anatomy-status-bar {
        padding: 0 1rem;
        font-size: 0.8rem;
    }

    .status-left,
    .status-right {
        gap: 1rem;
    }

    .progress-bar {
        width: 120px;
    }

    .model-placeholder {
        padding: 2rem;
        max-width: 90%;
    }

    .model-placeholder h2 {
        font-size: 1.5rem;
    }

    .model-features {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .anatomy-navbar {
        padding: 0 0.5rem;
    }

    .nav-back-btn {
        padding: 0.5rem;
        min-width: 40px;
    }

    .view-mode-switcher {
        transform: scale(0.8);
    }

    .anatomy-sidebar {
        flex-direction: column;
        height: auto;
        max-height: 200px;
        overflow-y: auto;
    }

    .sidebar-section {
        min-width: auto;
    }

    .control-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .anatomy-status-bar {
        flex-direction: column;
        height: auto;
        padding: 0.5rem;
        gap: 0.5rem;
    }

    .status-left,
    .status-center,
    .status-right {
        width: 100%;
        justify-content: center;
    }

    .progress-indicator {
        flex-direction: column;
        gap: 0.5rem;
    }

    .model-placeholder {
        padding: 1.5rem;
    }

    .model-placeholder h2 {
        font-size: 1.25rem;
    }

    .floating-controls {
        bottom: 0.5rem;
        right: 0.5rem;
        flex-direction: row;
    }

    .floating-btn {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}

.voice-control-content {
    text-align: center;
}

.mic-indicator {
    width: 80px;
    height: 80px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 2rem;
}

.command-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    margin: 1rem 0;
}

.command {
    background: #ecf0f1;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    color: #2c3e50;
}

.voice-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 1rem 2rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    margin-top: 1rem;
}

.progress-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    padding: 0 2rem 2rem;
    max-width: 1600px;
    margin: 0 auto;
    width: 100%;
    box-sizing: border-box;
}

.learning-progress,
.next-steps {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.progress-stats {
    display: flex;
    justify-content: space-around;
    margin-top: 1rem;
}

.stat {
    text-align: center;
}

.stat-value {
    display: block;
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
}

.stat-label {
    font-size: 0.9rem;
    color: #7f8c8d;
}

.recommendations {
    display: grid;
    gap: 1rem;
    margin-top: 1rem;
}

.recommendation {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.recommendation i {
    color: #667eea;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 1.5rem;
    }

    .progress-section {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        padding: 0 1.5rem 2rem;
    }

    .system-header {
        padding: 2rem 1.5rem;
        min-height: 25vh;
    }

    .system-title h1 {
        font-size: 2rem;
    }
}

@media (max-width: 992px) {
    .system-controls {
        flex-direction: column;
        gap: 1.5rem;
        align-items: stretch;
    }

    .view-controls {
        justify-content: center;
        flex-wrap: wrap;
    }

    .control-btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }

    .viewer-container {
        height: 400px;
    }

    .panel-tabs {
        flex-wrap: wrap;
    }

    .tab-btn {
        flex: 1 1 auto;
        min-width: 120px;
        padding: 0.8rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
        gap: 1rem;
    }

    .system-header {
        padding: 1.5rem 1rem;
        min-height: 20vh;
    }

    .system-title h1 {
        font-size: 1.8rem;
    }

    .system-title p {
        font-size: 0.9rem;
    }

    .view-controls {
        gap: 0.3rem;
    }

    .control-btn {
        padding: 0.5rem 1rem;
        font-size: 0.8rem;
    }

    .viewer-container {
        height: 300px;
    }

    .viewer-controls {
        padding: 1rem;
    }

    .zoom-controls {
        justify-content: center;
        margin-bottom: 1rem;
    }

    .zoom-btn {
        width: 35px;
        height: 35px;
    }

    .panel-tabs {
        flex-direction: column;
    }

    .tab-btn {
        padding: 1rem;
        text-align: center;
        border-bottom: 1px solid #ecf0f1;
    }

    .tab-btn:last-child {
        border-bottom: none;
    }

    .tab-content {
        padding: 1rem;
    }

    .structures-list {
        gap: 0.8rem;
    }

    .structure-item {
        padding: 0.8rem;
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .structure-info h4 {
        font-size: 1rem;
    }

    .structure-info p {
        font-size: 0.8rem;
    }

    .procedures-list {
        gap: 1rem;
    }

    .procedure-item {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
        gap: 1rem;
    }

    .procedure-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .ai-insights-content {
        gap: 0.8rem;
    }

    .insight-card {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
        gap: 0.8rem;
    }

    .insight-icon {
        margin: 0 auto;
    }

    .command-list {
        flex-direction: column;
        align-items: center;
    }

    .command {
        width: 100%;
        text-align: center;
        padding: 0.8rem 1rem;
    }

    .progress-section {
        padding: 0 1rem 1.5rem;
    }

    .learning-progress,
    .next-steps {
        padding: 1.5rem;
    }

    .progress-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .stat {
        padding: 1rem;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .recommendations {
        gap: 0.8rem;
    }

    .recommendation {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .system-header {
        padding: 1rem;
        min-height: 15vh;
    }

    .system-title h1 {
        font-size: 1.5rem;
    }

    .system-title p {
        font-size: 0.8rem;
    }

    .main-content {
        padding: 0.5rem;
    }

    .control-btn {
        padding: 0.4rem 0.8rem;
        font-size: 0.7rem;
    }

    .viewer-container {
        height: 250px;
    }

    .model-features {
        flex-direction: column;
        gap: 0.5rem;
    }

    .feature {
        padding: 0.2rem 0.5rem;
        font-size: 0.7rem;
    }

    .tab-btn {
        padding: 0.8rem;
        font-size: 0.7rem;
    }

    .tab-content {
        padding: 0.8rem;
    }

    .structure-item,
    .procedure-item,
    .insight-card {
        padding: 0.8rem;
    }

    .learning-progress,
    .next-steps {
        padding: 1rem;
    }

    .stat-value {
        font-size: 1.5rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }
}
</style>

<script>
// Tab switching
document.querySelectorAll('.tab-btn').forEach(button => {
    button.addEventListener('click', function() {
        // Remove active class from all tabs and panes
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('active'));
        
        // Add active class to clicked tab
        this.classList.add('active');
        
        // Show corresponding pane
        const tabId = this.dataset.tab;
        document.getElementById(tabId).classList.add('active');
    });
});

// Audio controls
let audioEnabled = false;
let currentAudio = null;

document.getElementById('start-narration').addEventListener('click', function() {
    audioEnabled = true;
    this.style.display = 'none';
    document.getElementById('stop-narration').style.display = 'inline-block';
    
    // Start narration
    const text = "Welcome to the 3D anatomy exploration. You can interact with the model by clicking on structures to learn more about them.";
    speak(text);
});

document.getElementById('stop-narration').addEventListener('click', function() {
    audioEnabled = false;
    if (currentAudio) {
        speechSynthesis.cancel();
        currentAudio = null;
    }
    this.style.display = 'none';
    document.getElementById('start-narration').style.display = 'inline-block';
});

function speak(text) {
    if (!audioEnabled) return;
    
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'en-US';
    currentAudio = utterance;
    speechSynthesis.speak(utterance);
}

// 3D Model controls (simulation)
function zoomIn() {
    console.log('Zooming in on 3D model');
    // In real implementation, this would control the 3D model
}

function zoomOut() {
    console.log('Zooming out on 3D model');
}

function resetView() {
    console.log('Resetting 3D model view');
}

function highlightStructure(structure) {
    console.log('Highlighting structure:', structure);
    
    // Simulate highlighting and provide information
    const info = {
        'heart': 'The heart is a muscular organ that pumps blood throughout the body.',
        'arteries': 'Arteries carry oxygenated blood away from the heart to body tissues.',
        'veins': 'Veins return deoxygenated blood back to the heart.'
    };
    
    if (audioEnabled && info[structure]) {
        speak(info[structure]);
    }
}

// Voice control simulation
document.getElementById('start-voice').addEventListener('click', function() {
    if ('webkitSpeechRecognition' in window) {
        const recognition = new webkitSpeechRecognition();
        recognition.continuous = false;
        recognition.interimResults = false;
        recognition.lang = 'en-US';
        
        recognition.onstart = function() {
            this.textContent = 'Listening...';
        };
        
        recognition.onresult = function(event) {
            const command = event.results[0][0].transcript.toLowerCase();
            processVoiceCommand(command);
        };
        
        recognition.onerror = function() {
            alert('Voice recognition error. Please try again.');
        };
        
        recognition.onend = function() {
            document.getElementById('start-voice').textContent = 'Start Voice Control';
        };
        
        recognition.start();
    } else {
        alert('Voice recognition not supported in this browser.');
    }
});

function processVoiceCommand(command) {
    console.log('Voice command:', command);
    
    if (command.includes('heart')) {
        highlightStructure('heart');
    } else if (command.includes('artery') || command.includes('arteries')) {
        highlightStructure('arteries');
    } else if (command.includes('vein') || command.includes('veins')) {
        highlightStructure('veins');
    } else if (command.includes('quiz')) {
        alert('Starting quiz mode...');
    } else {
        speak('Command not recognized. Try saying "show me the heart" or "start quiz mode".');
    }
}

// Practice procedure simulation
document.querySelectorAll('.practice-btn').forEach(button => {
    button.addEventListener('click', function() {
        const procedure = this.closest('.procedure-item').querySelector('h4').textContent;
        alert(`Starting ${procedure} simulation with haptic feedback and real-time assessment.`);
    });
});

// View mode switching
document.querySelectorAll('.control-btn').forEach(button => {
    button.addEventListener('click', function() {
        document.querySelectorAll('.control-btn').forEach(btn => btn.classList.remove('active'));
        this.classList.add('active');

        const view = this.dataset.view;
        if (view === 'ar') {
            alert('AR Mode: This would activate camera and overlay 3D anatomy on real-world objects.');
        } else if (view === 'quiz') {
            alert('Quiz Mode: Interactive questions about visible structures.');
        }
    });
});

// Additional modern interface functions
function updateProgress(percentage) {
    const progressFill = document.querySelector('.progress-fill');
    const progressText = document.querySelector('.progress-text');
    if (progressFill) {
        progressFill.style.width = percentage + '%';
    }
    if (progressText) {
        progressText.textContent = `Learning Progress: ${percentage}%`;
    }
}

function updateLayerStatus() {
    const statusText = document.querySelector('.status-left .status-item:nth-child(2) span');
    if (statusText) {
        statusText.textContent = `${visibleLayers} Layers Visible`;
    }
}

function startSessionTimer() {
    setInterval(function() {
        const elapsed = Math.floor((Date.now() - sessionStartTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        const timeString = `${minutes}:${seconds.toString().padStart(2, '0')}`;

        const timeDisplay = document.querySelector('.status-right .status-item span');
        if (timeDisplay) {
            timeDisplay.textContent = `Session: ${timeString}`;
        }
    }, 1000);
}

function updateSystemName() {
    const systemId = window.location.pathname.split('/').pop();
    const systemNames = {
        'cardiovascular': 'Cardiovascular System Explorer',
        'respiratory': 'Respiratory System Explorer',
        'nervous': 'Nervous System Explorer',
        'musculoskeletal': 'Musculoskeletal System Explorer'
    };

    const systemName = systemNames[systemId] || '3D Anatomy Explorer';
    const titleElement = document.querySelector('.nav-title');
    if (titleElement) {
        titleElement.textContent = systemName;
    }
}

function setupControlButtons() {
    // Zoom controls
    const zoomInBtn = document.querySelector('[onclick="zoomIn()"]');
    const zoomOutBtn = document.querySelector('[onclick="zoomOut()"]');
    const resetBtn = document.querySelector('[onclick="resetView()"]');

    if (zoomInBtn) {
        zoomInBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showNotification('Zooming in on 3D model');
        });
    }

    if (zoomOutBtn) {
        zoomOutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showNotification('Zooming out on 3D model');
        });
    }

    if (resetBtn) {
        resetBtn.addEventListener('click', function(e) {
            e.preventDefault();
            showNotification('Resetting camera view');
        });
    }
}

function setupSearch() {
    const searchInput = document.querySelector('.search-box input');
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();
            const structureCards = document.querySelectorAll('.structure-card');

            structureCards.forEach(card => {
                const title = card.querySelector('h4').textContent.toLowerCase();
                const description = card.querySelector('p').textContent.toLowerCase();

                if (title.includes(query) || description.includes(query)) {
                    card.style.display = 'flex';
                } else {
                    card.style.display = query ? 'none' : 'flex';
                }
            });
        });
    }
}

function showNotification(message) {
    // Create a simple notification
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed;
        top: 100px;
        right: 20px;
        background: var(--primary-color);
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: var(--shadow-lg);
        z-index: 10000;
        animation: slideIn 0.3s ease-out;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOut 0.3s ease-in';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}

// Add CSS animations for notifications
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
