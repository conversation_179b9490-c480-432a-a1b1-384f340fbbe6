from flask import Blueprint, render_template, redirect, url_for, flash, session, request, jsonify, current_app
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
from .forms import RegistrationForm, LoginForm, ProfileForm
from .models import User
from . import mongo
from bson import ObjectId
from pymongo.errors import DuplicateKeyError
from datetime import datetime, timezone
import os
import uuid

bp = Blueprint('main', __name__)

def calculate_profile_completion(user):
    """Calculate profile completion percentage"""
    total_fields = 7
    completed_fields = 0

    # Check basic fields
    if user.get('full_name'): completed_fields += 1
    if user.get('email'): completed_fields += 1

    # Check profile data
    profile_data = user.get('profile_data', {})
    if profile_data.get('bio'): completed_fields += 1
    if profile_data.get('location'): completed_fields += 1
    if profile_data.get('website'): completed_fields += 1

    # Check preferences (always completed if they exist)
    preferences = profile_data.get('preferences', {})
    if preferences: completed_fields += 2

    return int((completed_fields / total_fields) * 100)

@bp.route('/')
def index():
    if 'user_id' in session:
        return redirect(url_for('main.dashboard'))
    return redirect(url_for('main.login'))

@bp.route('/register', methods=['GET', 'POST'])
def register():
    form = RegistrationForm()
    if form.validate_on_submit():
        try:
            # Check if username or email already exists
            existing_user = mongo.db.users.find_one({
                '$or': [
                    {'username': form.username.data},
                    {'email': form.email.data}
                ]
            })
            if existing_user:
                if existing_user['username'] == form.username.data:
                    flash('Username already exists. Please choose a different one.', 'danger')
                else:
                    flash('Email already exists. Please use a different email.', 'danger')
                return render_template('register.html', form=form)

            hashed_password = generate_password_hash(form.password.data, method='scrypt')
            user = User(
                username=form.username.data,
                password=hashed_password,
                email=form.email.data,
                full_name=form.full_name.data
            )
            result = mongo.db.users.insert_one(user.to_dict())
            print(f"User registered successfully! User ID: {result.inserted_id}")
            print(f"User data: {user.to_dict()}")
            flash('Registration successful! Please log in.', 'success')
            return redirect(url_for('main.login'))
        except Exception as e:
            flash('Registration failed. Please try again.', 'danger')
            print(f"Registration error: {e}")
            return render_template('register.html', form=form)
    return render_template('register.html', form=form)

@bp.route('/login', methods=['GET', 'POST'])
def login():
    form = LoginForm()
    if form.validate_on_submit():
        user = mongo.db.users.find_one({'username': form.username.data})
        print(f"Login attempt for username: {form.username.data}")
        print(f"User found: {user is not None}")
        if user and check_password_hash(user['password'], form.password.data):
            # Update last login time
            mongo.db.users.update_one(
                {'_id': user['_id']},
                {'$set': {'last_login': datetime.now(timezone.utc)}}
            )
            session['user_id'] = str(user['_id'])
            session['username'] = user['username']
            print(f"Login successful for user: {user['username']}")
            flash(f'Welcome back, {user.get("full_name", user["username"])}!', 'success')
            return redirect(url_for('main.dashboard'))
        else:
            print("Login failed: Invalid credentials")
        flash('Login failed. Check your username and/or password.', 'danger')
    return render_template('login.html', form=form)

@bp.route('/dashboard')
def dashboard():
    if 'user_id' not in session:
        return redirect(url_for('main.login'))
    user = mongo.db.users.find_one({'_id': ObjectId(session['user_id'])})
    if not user:
        flash('User not found. Please log in again.', 'danger')
        return redirect(url_for('main.login'))

    # Get user statistics
    user_posts_count = mongo.db.posts.count_documents({'author_id': user['_id']})
    user_comments_count = mongo.db.comments.count_documents({'author_id': user['_id']})

    user_stats = {
        'member_since': user.get('created_at', 'Unknown'),
        'last_login': user.get('last_login', 'Never'),
        'total_users': mongo.db.users.count_documents({}),
        'profile_completion': calculate_profile_completion(user),
        'user_posts': user_posts_count,
        'user_comments': user_comments_count,
        'total_posts': mongo.db.posts.count_documents({}),
        'total_comments': mongo.db.comments.count_documents({})
    }

    return render_template('dashboard.html', user=user, stats=user_stats)

@bp.route('/profile', methods=['GET', 'POST'])
def profile():
    if 'user_id' not in session:
        return redirect(url_for('main.login'))

    user = mongo.db.users.find_one({'_id': ObjectId(session['user_id'])})
    if not user:
        flash('User not found. Please log in again.', 'danger')
        return redirect(url_for('main.login'))

    form = ProfileForm()

    if form.validate_on_submit():
        try:
            # Check if email is being changed and if it's already taken
            if form.email.data != user.get('email'):
                existing_email = mongo.db.users.find_one({
                    'email': form.email.data,
                    '_id': {'$ne': user['_id']}
                })
                if existing_email:
                    flash('Email already exists. Please use a different email.', 'danger')
                    return render_template('profile.html', form=form, user=user)

            # Handle profile picture upload
            profile_picture = user.get('profile_data', {}).get('profile_picture', '')
            if form.profile_picture.data:
                new_picture = save_uploaded_file(form.profile_picture.data, current_app.config['UPLOAD_FOLDER'])
                if new_picture:
                    # Delete old profile picture if it exists
                    if profile_picture:
                        old_picture_path = os.path.join(current_app.config['UPLOAD_FOLDER'], profile_picture)
                        if os.path.exists(old_picture_path):
                            os.remove(old_picture_path)
                    profile_picture = new_picture

            # Update user profile
            update_data = {
                'full_name': form.full_name.data,
                'email': form.email.data,
                'profile_data.bio': form.bio.data,
                'profile_data.location': form.location.data,
                'profile_data.website': form.website.data,
                'profile_data.profile_picture': profile_picture,
                'profile_data.preferences.theme': form.theme.data,
                'profile_data.preferences.notifications': form.notifications.data,
                'profile_data.preferences.privacy': form.privacy.data
            }

            mongo.db.users.update_one(
                {'_id': user['_id']},
                {'$set': update_data}
            )

            flash('Profile updated successfully!', 'success')
            return redirect(url_for('main.profile'))

        except Exception as e:
            flash('Profile update failed. Please try again.', 'danger')
            print(f"Profile update error: {e}")

    # Pre-populate form with current user data
    if not form.is_submitted():
        form.full_name.data = user.get('full_name', '')
        form.email.data = user.get('email', '')
        profile_data = user.get('profile_data', {})
        form.bio.data = profile_data.get('bio', '')
        form.location.data = profile_data.get('location', '')
        form.website.data = profile_data.get('website', '')
        preferences = profile_data.get('preferences', {})
        form.theme.data = preferences.get('theme', 'light')
        form.notifications.data = preferences.get('notifications', True)
        form.privacy.data = preferences.get('privacy', 'public')

    return render_template('profile.html', form=form, user=user)

@bp.route('/logout')
def logout():
    username = session.get('username', 'User')
    session.clear()
    flash(f'Goodbye {username}! You have been logged out.', 'success')
    return redirect(url_for('main.login'))

@bp.route('/admin/users')
def admin_users():
    """Simple admin route to view all users (for testing purposes)"""
    try:
        users = list(mongo.db.users.find({}, {'password': 0}))  # Exclude passwords
        total_users = len(users)

        # Convert ObjectId to string for display
        for user in users:
            user['_id'] = str(user['_id'])
            if user.get('created_at'):
                user['created_at'] = user['created_at'].strftime('%Y-%m-%d %H:%M:%S') if hasattr(user['created_at'], 'strftime') else str(user['created_at'])
            if user.get('last_login'):
                user['last_login'] = user['last_login'].strftime('%Y-%m-%d %H:%M:%S') if hasattr(user['last_login'], 'strftime') else str(user['last_login'])

        return f"""
        <html>
        <head>
            <title>Admin - All Users</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 2rem; background: #f5f5f5; }}
                .container {{ background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                h1 {{ color: #333; }}
                .user-card {{ background: #f8f9fa; padding: 1rem; margin: 1rem 0; border-radius: 8px; border-left: 4px solid #667eea; }}
                .user-info {{ margin: 0.5rem 0; }}
                .back-link {{ display: inline-block; margin-top: 1rem; padding: 0.5rem 1rem; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }}
                .stats {{ background: #e3f2fd; padding: 1rem; border-radius: 8px; margin-bottom: 2rem; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🔧 Admin Panel - All Users</h1>
                <div class="stats">
                    <h3>📊 Statistics</h3>
                    <p><strong>Total Users:</strong> {total_users}</p>
                    <p><strong>Database:</strong> secure-flask-app</p>
                    <p><strong>Collection:</strong> users</p>
                </div>

                <h2>👥 User List</h2>
                {''.join([f'''
                <div class="user-card">
                    <div class="user-info"><strong>ID:</strong> {user.get('_id', 'N/A')}</div>
                    <div class="user-info"><strong>Username:</strong> {user.get('username', 'N/A')}</div>
                    <div class="user-info"><strong>Full Name:</strong> {user.get('full_name', 'N/A')}</div>
                    <div class="user-info"><strong>Email:</strong> {user.get('email', 'N/A')}</div>
                    <div class="user-info"><strong>Created:</strong> {user.get('created_at', 'N/A')}</div>
                    <div class="user-info"><strong>Last Login:</strong> {user.get('last_login', 'Never')}</div>
                    <div class="user-info"><strong>Bio:</strong> {user.get('profile_data', {}).get('bio', 'No bio')}</div>
                </div>
                ''' for user in users])}

                <a href="/" class="back-link">← Back to App</a>
            </div>
        </body>
        </html>
        """
    except Exception as e:
        return f"<h1>Error</h1><p>Could not fetch users: {str(e)}</p><a href='/'>Back to App</a>"


@bp.route('/admin/approve-all-blogs')
def approve_all_blogs():
    """Admin route to approve all pending blogs"""
    try:
        # Update all pending blogs to approved
        result = mongo.db.blogs.update_many(
            {'status': 'pending'},
            {'$set': {'status': 'approved'}}
        )

        return f"""
        <html>
        <head>
            <title>Blog Approval Update</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 2rem; background: #f5f5f5; }}
                .container {{ background: white; padding: 2rem; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                h1 {{ color: #333; }}
                .success {{ background: #d4edda; color: #155724; padding: 1rem; border-radius: 8px; margin: 1rem 0; }}
                .back-link {{ display: inline-block; margin-top: 1rem; padding: 0.5rem 1rem; background: #667eea; color: white; text-decoration: none; border-radius: 5px; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🚀 Blog Approval Update</h1>
                <div class="success">
                    <strong>Success!</strong> Updated {result.modified_count} blogs from pending to approved status.
                </div>
                <p>All blogs are now immediately visible to all users.</p>
                <a href="/blogs" class="back-link">View All Blogs</a>
                <a href="/admin/users" class="back-link">Back to Admin</a>
                <a href="/" class="back-link">Back to App</a>
            </div>
        </body>
        </html>
        """
    except Exception as e:
        return f"<h1>Error</h1><p>Could not update blogs: {str(e)}</p><a href='/'>Back to App</a>"


# Helper functions for file uploads
def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in current_app.config['ALLOWED_EXTENSIONS']


def save_uploaded_file(file, upload_folder):
    """Save uploaded file and return filename"""
    if file and allowed_file(file.filename):
        # Generate unique filename
        filename = secure_filename(file.filename)
        name, ext = os.path.splitext(filename)
        unique_filename = f"{name}_{uuid.uuid4().hex[:8]}{ext}"

        # Ensure upload directory exists
        os.makedirs(upload_folder, exist_ok=True)

        # Save file
        file_path = os.path.join(upload_folder, unique_filename)
        file.save(file_path)
        return unique_filename
    return None


# Medical Education Routes
@bp.route('/lessons')
def lessons():
    """Medical education lessons main page"""
    if 'user_id' not in session:
        flash('Please log in to access medical lessons.', 'danger')
        return redirect(url_for('main.login'))

    # Available learning modules
    learning_modules = [
        {
            'id': 'diseases',
            'name': 'Disease Education',
            'name_kannada': 'ರೋಗ ಶಿಕ್ಷಣ',
            'name_hindi': 'रोग शिक्षा',
            'name_telugu': 'వ్యాధి విద్య',
            'description': 'Learn about common diseases and conditions',
            'icon': 'fas fa-heartbeat',
            'modules': [
                {
                    'id': 'diabetes',
                    'name': 'Diabetes',
                    'name_kannada': 'ಮಧುಮೇಹ',
                    'name_hindi': 'मधुमेह',
                    'name_telugu': 'మధుమేహం',
                    'description': 'Learn about diabetes management and prevention',
                    'difficulty': 'Beginner',
                    'duration': '15 mins'
                },
                {
                    'id': 'stroke',
                    'name': 'Stroke',
                    'name_kannada': 'ಪಾರ್ಶ್ವವಾಯು',
                    'name_hindi': 'स्ट्रोक',
                    'name_telugu': 'స్ట్రోక్',
                    'description': 'Understanding stroke symptoms and emergency response',
                    'difficulty': 'Intermediate',
                    'duration': '20 mins'
                },
                {
                    'id': 'dengue',
                    'name': 'Dengue',
                    'name_kannada': 'ಡೆಂಗ್ಯೂ',
                    'name_hindi': 'डेंगू',
                    'name_telugu': 'డెంగ్యూ',
                    'description': 'Prevention and treatment of dengue fever',
                    'difficulty': 'Beginner',
                    'duration': '12 mins'
                }
            ]
        },
        {
            'id': 'anatomy',
            'name': 'Virtual Anatomy Lab',
            'name_kannada': 'ವರ್ಚುವಲ್ ಅನಾಟಮಿ ಲ್ಯಾಬ್',
            'name_hindi': 'वर्चुअल एनाटॉमी लैब',
            'name_telugu': 'వర్చువల్ అనాటమీ ల్యాబ్',
            'description': 'AI-powered 3D anatomy learning with personalized paths',
            'icon': 'fas fa-user-md',
            'modules': [
                {
                    'id': 'cardiovascular',
                    'name': 'Cardiovascular System',
                    'name_kannada': 'ಹೃದಯರಕ್ತನಾಳ ವ್ಯವಸ್ಥೆ',
                    'name_hindi': 'हृदय प्रणाली',
                    'name_telugu': 'హృదయ వ్యవస్థ',
                    'description': 'Interactive 3D heart and blood vessel exploration',
                    'difficulty': 'Intermediate',
                    'duration': '30 mins'
                },
                {
                    'id': 'respiratory',
                    'name': 'Respiratory System',
                    'name_kannada': 'ಉಸಿರಾಟದ ವ್ಯವಸ್ಥೆ',
                    'name_hindi': 'श्वसन तंत्र',
                    'name_telugu': 'శ్వాస వ్యవస్థ',
                    'description': 'Lungs, airways, and breathing mechanisms',
                    'difficulty': 'Beginner',
                    'duration': '25 mins'
                },
                {
                    'id': 'nervous',
                    'name': 'Nervous System',
                    'name_kannada': 'ನರಮಂಡಲ',
                    'name_hindi': 'तंत्रिका तंत्र',
                    'name_telugu': 'నాడీ వ్యవస్థ',
                    'description': 'Brain, spinal cord, and neural pathways',
                    'difficulty': 'Advanced',
                    'duration': '45 mins'
                }
            ]
        }
    ]

    return render_template('lessons/index.html', learning_modules=learning_modules)


@bp.route('/anatomy-lab')
def anatomy_lab():
    """Virtual Anatomy Lab main page"""
    if 'user_id' not in session:
        flash('Please log in to access the Virtual Anatomy Lab.', 'danger')
        return redirect(url_for('main.login'))

    # Anatomy systems available
    anatomy_systems = [
        {
            'id': 'cardiovascular',
            'name': 'Cardiovascular System',
            'name_kannada': 'ಹೃದಯರಕ್ತನಾಳ ವ್ಯವಸ್ಥೆ',
            'name_hindi': 'हृदय प्रणाली',
            'name_telugu': 'హృదయ వ్యవస్థ',
            'description': 'Interactive 3D heart and blood vessel exploration',
            'icon': 'fas fa-heart',
            'difficulty': 'Intermediate',
            'duration': '30 mins',
            'structures': ['Heart', 'Arteries', 'Veins', 'Capillaries'],
            'procedures': ['Blood Pressure Measurement', 'ECG Reading', 'Pulse Assessment']
        },
        {
            'id': 'respiratory',
            'name': 'Respiratory System',
            'name_kannada': 'ಉಸಿರಾಟದ ವ್ಯವಸ್ಥೆ',
            'name_hindi': 'श्वसन तंत्र',
            'name_telugu': 'శ్వాస వ్యవస్థ',
            'description': 'Lungs, airways, and breathing mechanisms',
            'icon': 'fas fa-lungs',
            'difficulty': 'Beginner',
            'duration': '25 mins',
            'structures': ['Lungs', 'Trachea', 'Bronchi', 'Alveoli'],
            'procedures': ['Spirometry', 'Chest Examination', 'Oxygen Saturation']
        },
        {
            'id': 'nervous',
            'name': 'Nervous System',
            'name_kannada': 'ನರಮಂಡಲ',
            'name_hindi': 'तंत्रिका तंत्र',
            'name_telugu': 'నాడీ వ్యవస్థ',
            'description': 'Brain, spinal cord, and neural pathways',
            'icon': 'fas fa-brain',
            'difficulty': 'Advanced',
            'duration': '45 mins',
            'structures': ['Brain', 'Spinal Cord', 'Nerves', 'Neurons'],
            'procedures': ['Neurological Examination', 'Reflex Testing', 'Cognitive Assessment']
        },
        {
            'id': 'musculoskeletal',
            'name': 'Musculoskeletal System',
            'name_kannada': 'ಸ್ನಾಯು-ಅಸ್ಥಿ ವ್ಯವಸ್ಥೆ',
            'name_hindi': 'मांसपेशी-कंकाल तंत्र',
            'name_telugu': 'కండర-అస్థి వ్యవస్థ',
            'description': 'Bones, muscles, joints, and movement',
            'icon': 'fas fa-bone',
            'difficulty': 'Intermediate',
            'duration': '35 mins',
            'structures': ['Bones', 'Muscles', 'Joints', 'Ligaments'],
            'procedures': ['Joint Examination', 'Range of Motion', 'Muscle Strength Testing']
        }
    ]

    return render_template('anatomy/lab.html', anatomy_systems=anatomy_systems)


@bp.route('/anatomy-lab/<system_id>')
def view_anatomy_system(system_id):
    """View specific anatomy system with 3D models"""
    if 'user_id' not in session:
        flash('Please log in to access anatomy systems.', 'danger')
        return redirect(url_for('main.login'))

    # Get user's learning progress for personalization
    user = mongo.db.users.find_one({'_id': ObjectId(session['user_id'])})
    user_progress = user.get('medical_progress', {}).get('anatomy_progress', {})

    return render_template('anatomy/system.html',
                         system_id=system_id,
                         user_progress=user_progress)


@bp.route('/lesson/<disease_id>')
def view_lesson(disease_id):
    """View a specific medical lesson"""
    if 'user_id' not in session:
        flash('Please log in to access medical lessons.', 'danger')
        return redirect(url_for('main.login'))

    # Medical content for different diseases
    lessons_content = {
        'diabetes': {
            'name': 'Diabetes',
            'name_kannada': 'ಮಧುಮೇಹ',
            'name_hindi': 'मधुमेह',
            'name_telugu': 'మధుమేహం',
            'content': {
                'english': "Diabetes is a condition where your body cannot properly use sugar from food. This happens when your pancreas doesn't make enough insulin or your body can't use insulin well. High blood sugar can damage your eyes, kidneys, nerves, and heart over time.",
                'kannada': "ಮಧುಮೇಹವು ನಿಮ್ಮ ದೇಹವು ಆಹಾರದಿಂದ ಸಕ್ಕರೆಯನ್ನು ಸರಿಯಾಗಿ ಬಳಸಲು ಸಾಧ್ಯವಾಗದ ಸ್ಥಿತಿಯಾಗಿದೆ. ನಿಮ್ಮ ಮೇದೋಜೀರಕ ಗ್ರಂಥಿ ಸಾಕಷ್ಟು ಇನ್ಸುಲಿನ್ ಉತ್ಪಾದಿಸದಿದ್ದಾಗ ಅಥವಾ ನಿಮ್ಮ ದೇಹವು ಇನ್ಸುಲಿನ್ ಅನ್ನು ಚೆನ್ನಾಗಿ ಬಳಸಲು ಸಾಧ್ಯವಾಗದಿದ್ದಾಗ ಇದು ಸಂಭವಿಸುತ್ತದೆ.",
                'hindi': "मधुमेह एक ऐसी स्थिति है जहाँ आपका शरीर भोजन से चीनी का सही तरीके से उपयोग नहीं कर सकता। यह तब होता है जब आपका अग्न्याशय पर्याप्त इंसुलिन नहीं बनाता या आपका शरीर इंसुलिन का अच्छी तरह से उपयोग नहीं कर सकता।",
                'telugu': "మధుమేహం అనేది మీ శరీరం ఆహారం నుండి చక్కెరను సరిగ్గా ఉపయోగించలేని పరిస్థితి. మీ క్లోమగ్రంథి తగినంత ఇన్సులిన్ తయారు చేయనప్పుడు లేదా మీ శరీరం ఇన్సులిన్‌ను బాగా ఉపయోగించలేనప్పుడు ఇది జరుగుతుంది."
            },
            'symptoms': {
                'english': "• Frequent urination\n• Excessive thirst\n• Unexplained weight loss\n• Fatigue\n• Blurred vision",
                'kannada': "• ಆಗಾಗ್ಗೆ ಮೂತ್ರ ವಿಸರ್ಜನೆ\n• ಅತಿಯಾದ ಬಾಯಾರಿಕೆ\n• ವಿವರಿಸಲಾಗದ ತೂಕ ನಷ್ಟ\n• ಆಯಾಸ\n• ಮಸುಕಾದ ದೃಷ್ಟಿ",
                'hindi': "• बार-बार पेशाब आना\n• अत्यधिक प्यास\n• अस्पष्ट वजन घटना\n• थकान\n• धुंधली दृष्टि",
                'telugu': "• తరచుగా మూత్రవిసర్జన\n• అధిక దాహం\n• వివరించలేని బరువు తగ్గడం\n• అలసట\n• అస్పష్టమైన దృష్టి"
            },
            'prevention': {
                'english': "• Eat healthy foods\n• Exercise regularly\n• Maintain healthy weight\n• Avoid smoking\n• Regular health checkups",
                'kannada': "• ಆರೋಗ್ಯಕರ ಆಹಾರ ಸೇವಿಸಿ\n• ನಿಯಮಿತವಾಗಿ ವ್ಯಾಯಾಮ ಮಾಡಿ\n• ಆರೋಗ್ಯಕರ ತೂಕವನ್ನು ಕಾಪಾಡಿ\n• ಧೂಮಪಾನವನ್ನು ತಪ್ಪಿಸಿ\n• ನಿಯಮಿತ ಆರೋಗ್ಯ ಪರೀಕ್ಷೆಗಳು",
                'hindi': "• स्वस्थ भोजन खाएं\n• नियमित व्यायाम करें\n• स्वस्थ वजन बनाए रखें\n• धूम्रपान से बचें\n• नियमित स्वास्थ्य जांच",
                'telugu': "• ఆరోగ్యకరమైన ఆహారం తీసుకోండి\n• క్రమం తప్పకుండా వ్యాయామం చేయండి\n• ఆరోగ్యకరమైన బరువును నిర్వహించండి\n• ధూమపానం మానుకోండి\n• క్రమం తప్పకుండా ఆరోగ్య పరీక్షలు"
            }
        }
    }

    if disease_id not in lessons_content:
        flash('Lesson not found.', 'danger')
        return redirect(url_for('main.lessons'))

    lesson = lessons_content[disease_id]
    return render_template('lessons/view_lesson.html', lesson=lesson, disease_id=disease_id)


@bp.route('/chatbot')
def chatbot():
    """AI Medical Chatbot Interface"""
    if 'user_id' not in session:
        flash('Please log in to access the medical chatbot.', 'danger')
        return redirect(url_for('main.login'))

    return render_template('chatbot/index.html')


@bp.route('/chatbot/ask', methods=['POST'])
def chatbot_ask():
    """Handle chatbot questions"""
    if 'user_id' not in session:
        return jsonify({'error': 'Not logged in'})

    data = request.get_json()
    question = data.get('question', '').lower()
    language = data.get('language', 'english')

    # Sample responses for common medical questions
    responses = {
        'diabetes': {
            'english': "Diabetes is a condition where blood sugar levels are too high. Common symptoms include frequent urination, excessive thirst, and fatigue. Please consult a doctor for proper diagnosis and treatment.",
            'kannada': "ಮಧುಮೇಹವು ರಕ್ತದಲ್ಲಿನ ಸಕ್ಕರೆಯ ಮಟ್ಟವು ತುಂಬಾ ಹೆಚ್ಚಿರುವ ಸ್ಥಿತಿಯಾಗಿದೆ. ಸಾಮಾನ್ಯ ಲಕ್ಷಣಗಳೆಂದರೆ ಆಗಾಗ್ಗೆ ಮೂತ್ರ ವಿಸರ್ಜನೆ, ಅತಿಯಾದ ಬಾಯಾರಿಕೆ ಮತ್ತು ಆಯಾಸ.",
            'hindi': "मधुमेह एक ऐसी स्थिति है जहाँ रक्त शर्करा का स्तर बहुत अधिक होता है। सामान्य लक्षणों में बार-बार पेशाब आना, अत्यधिक प्यास और थकान शामिल है।",
            'telugu': "మధుమేహం అనేది రక్తంలో చక్కెర స్థాయిలు చాలా ఎక్కువగా ఉండే పరిస్థితి. సాధారణ లక్షణాలలో తరచుగా మూత్రవిసర్జన, అధిక దాహం మరియు అలసట ఉన్నాయి."
        },
        'fever': {
            'english': "Fever is your body's natural response to infection. Rest, drink fluids, and take paracetamol if needed. See a doctor if fever persists or is very high.",
            'kannada': "ಜ್ವರವು ಸೋಂಕಿಗೆ ನಿಮ್ಮ ದೇಹದ ನೈಸರ್ಗಿಕ ಪ್ರತಿಕ್ರಿಯೆಯಾಗಿದೆ. ವಿಶ್ರಾಂತಿ ತೆಗೆದುಕೊಳ್ಳಿ, ದ್ರವಗಳನ್ನು ಕುಡಿಯಿರಿ ಮತ್ತು ಅಗತ್ಯವಿದ್ದರೆ ಪ್ಯಾರಸಿಟಮಾಲ್ ತೆಗೆದುಕೊಳ್ಳಿ.",
            'hindi': "बुखार संक्रमण के लिए आपके शरीर की प्राकृतिक प्रतिक्रिया है। आराम करें, तरल पदार्थ पिएं, और यदि आवश्यक हो तो पैरासिटामोल लें।",
            'telugu': "జ్వరం అంటే ఇన్ఫెక్షన్‌కు మీ శరీరం యొక్క సహజ ప్రతిస్పందన. విశ్రాంతి తీసుకోండి, ద్రవాలు తాగండి మరియు అవసరమైతే పారాసిటమాల్ తీసుకోండి."
        }
    }

    # Simple keyword matching for demo
    response_text = "I'm here to help with basic medical information. Please consult a healthcare professional for proper medical advice."

    for condition, condition_responses in responses.items():
        if condition in question:
            response_text = condition_responses.get(language, condition_responses['english'])
            break

    return jsonify({'response': response_text})


@bp.route('/lesson/<disease_id>/quiz')
def lesson_quiz(disease_id):
    """Interactive quiz for medical lessons"""
    if 'user_id' not in session:
        flash('Please log in to take quizzes.', 'danger')
        return redirect(url_for('main.login'))

    # Sample quiz questions for diabetes
    quizzes = {
        'diabetes': {
            'questions': [
                {
                    'question': 'What is the main cause of Type 2 diabetes?',
                    'question_kannada': 'ಟೈಪ್ 2 ಮಧುಮೇಹದ ಮುಖ್ಯ ಕಾರಣ ಏನು?',
                    'question_hindi': 'टाइप 2 मधुमेह का मुख्य कारण क्या है?',
                    'question_telugu': 'టైప్ 2 మధుమేహానికి ప్రధాన కారణం ఏమిటి?',
                    'options': [
                        'Insulin resistance',
                        'Too much exercise',
                        'Eating too much fruit',
                        'Drinking water'
                    ],
                    'correct': 0
                },
                {
                    'question': 'Which symptom is common in diabetes?',
                    'question_kannada': 'ಮಧುಮೇಹದಲ್ಲಿ ಯಾವ ಲಕ್ಷಣ ಸಾಮಾನ್ಯವಾಗಿದೆ?',
                    'question_hindi': 'मधुमेह में कौन सा लक्षण आम है?',
                    'question_telugu': 'మధుమేహంలో ఏ లక్షణం సాధారణం?',
                    'options': [
                        'Frequent urination',
                        'Better vision',
                        'Increased strength',
                        'Less appetite'
                    ],
                    'correct': 0
                }
            ]
        }
    }

    if disease_id not in quizzes:
        flash('Quiz not found.', 'danger')
        return redirect(url_for('main.lessons'))

    quiz = quizzes[disease_id]
    return render_template('lessons/quiz.html', quiz=quiz, disease_id=disease_id)


@bp.route('/progress')
def user_progress():
    """Track user's learning progress"""
    if 'user_id' not in session:
        flash('Please log in to view your progress.', 'danger')
        return redirect(url_for('main.login'))

    # Sample progress data - in real app this would come from database
    progress_data = {
        'completed_lessons': ['diabetes'],
        'quiz_scores': {
            'diabetes': 85
        },
        'total_lessons': 3,
        'certificates_earned': 1
    }

    return render_template('progress/index.html', progress=progress_data)


@bp.route('/my-progress')
def my_progress():
    """View current user's learning progress"""
    if 'user_id' not in session:
        flash('Please log in to view your progress.', 'danger')
        return redirect(url_for('main.login'))

    # Sample progress data - in real app this would come from database
    user_progress = {
        'completed_lessons': ['diabetes'],
        'quiz_scores': {
            'diabetes': 85,
            'stroke': 0,
            'dengue': 0
        },
        'certificates': [
            {
                'disease': 'diabetes',
                'score': 85,
                'date': '2024-01-15',
                'certificate_id': 'CERT-DM-001'
            }
        ],
        'total_study_time': 45,  # minutes
        'streak_days': 3
    }

    return render_template('progress/my_progress.html', progress=user_progress)


# Medical Education Content Generation Routes
@bp.route('/content/generate/<disease_id>')
def generate_content(disease_id):
    """Generate comprehensive medical content for diseases"""
    if 'user_id' not in session:
        flash('Please log in to access content generation.', 'danger')
        return redirect(url_for('main.login'))

    # Comprehensive medical content for MeduLearn
    medical_content = {
        'diabetes': {
            'name': 'Diabetes',
            'translations': {
                'kannada': 'ಮಧುಮೇಹ',
                'hindi': 'मधुमेह',
                'telugu': 'మధుమేహం'
            },
            'explanation': {
                'english': "Diabetes is a group of diseases that result in too much sugar in the blood. When you eat, your body breaks down food into glucose (sugar). Insulin, a hormone made by your pancreas, helps glucose get into your cells to give them energy. In diabetes, your body either doesn't make enough insulin or can't use it well.",
                'kannada': "ಮಧುಮೇಹವು ರಕ್ತದಲ್ಲಿ ಅತಿಯಾದ ಸಕ್ಕರೆಯನ್ನು ಉಂಟುಮಾಡುವ ರೋಗಗಳ ಗುಂಪಾಗಿದೆ. ನೀವು ಆಹಾರ ಸೇವಿಸಿದಾಗ, ನಿಮ್ಮ ದೇಹವು ಆಹಾರವನ್ನು ಗ್ಲೂಕೋಸ್ (ಸಕ್ಕರೆ) ಆಗಿ ವಿಭಜಿಸುತ್ತದೆ. ಇನ್ಸುಲಿನ್ ಎಂಬ ಹಾರ್ಮೋನ್ ಗ್ಲೂಕೋಸ್ ಅನ್ನು ಜೀವಕೋಶಗಳಿಗೆ ಪ್ರವೇಶಿಸಲು ಸಹಾಯ ಮಾಡುತ್ತದೆ.",
                'hindi': "मधुमेह रोगों का एक समूह है जिसके परिणामस्वरूप रक्त में बहुत अधिक चीनी हो जाती है। जब आप खाते हैं, तो आपका शरीर भोजन को ग्लूकोज (चीनी) में तोड़ देता है। इंसुलिन, आपके अग्न्याशय द्वारा बनाया गया एक हार्मोन, ग्लूकोज को आपकी कोशिकाओं में जाने में मदद करता है।",
                'telugu': "మధుమేహం అనేది రక్తంలో చాలా ఎక్కువ చక్కెర కలిగించే వ్యాధుల సమూహం. మీరు తింటే, మీ శరీరం ఆహారాన్ని గ్లూకోజ్ (చక్కెర)గా విభజిస్తుంది. ఇన్సులిన్ అనే హార్మోన్ గ్లూకోజ్‌ను మీ కణాలలోకి ప్రవేశించడానికి సహాయపడుతుంది."
            },
            'symptoms': {
                'english': ["Frequent urination", "Excessive thirst", "Unexplained weight loss", "Extreme fatigue", "Blurred vision", "Slow healing wounds"],
                'kannada': ["ಆಗಾಗ್ಗೆ ಮೂತ್ರ ವಿಸರ್ಜನೆ", "ಅತಿಯಾದ ಬಾಯಾರಿಕೆ", "ವಿವರಿಸಲಾಗದ ತೂಕ ನಷ್ಟ", "ತೀವ್ರ ಆಯಾಸ", "ಮಸುಕಾದ ದೃಷ್ಟಿ", "ನಿಧಾನವಾಗಿ ಗುಣಪಡಿಸುವ ಗಾಯಗಳು"],
                'hindi': ["बार-बार पेशाब आना", "अत्यधिक प्यास", "अस्पष्ट वजन घटना", "अत्यधिक थकान", "धुंधली दृष्टि", "धीरे-धीरे ठीक होने वाले घाव"],
                'telugu': ["తరచుగా మూత్రవిసర్జన", "అధిక దాహం", "వివరించలేని బరువు తగ్గడం", "తీవ్రమైన అలసట", "అస్పష్టమైన దృష్టి", "నెమ్మదిగా నయమయ్యే గాయాలు"]
            },
            'prevention': {
                'english': ["Maintain healthy weight", "Exercise regularly", "Eat balanced diet", "Limit sugar intake", "Regular health checkups", "Avoid smoking"],
                'kannada': ["ಆರೋಗ್ಯಕರ ತೂಕವನ್ನು ಕಾಪಾಡಿ", "ನಿಯಮಿತವಾಗಿ ವ್ಯಾಯಾಮ ಮಾಡಿ", "ಸಮತೋಲಿತ ಆಹಾರ ಸೇವಿಸಿ", "ಸಕ್ಕರೆ ಸೇವನೆಯನ್ನು ಮಿತಿಗೊಳಿಸಿ", "ನಿಯಮಿತ ಆರೋಗ್ಯ ಪರೀಕ್ಷೆಗಳು", "ಧೂಮಪಾನವನ್ನು ತಪ್ಪಿಸಿ"],
                'hindi': ["स्वस्थ वजन बनाए रखें", "नियमित व्यायाम करें", "संतुलित आहार लें", "चीनी का सेवन सीमित करें", "नियमित स्वास्थ्य जांच", "धूम्रपान से बचें"],
                'telugu': ["ఆరోగ్యకరమైన బరువును నిర్వహించండి", "క్రమం తప్పకుండా వ్యాయామం చేయండి", "సమతుల్య ఆహారం తీసుకోండి", "చక్కెర తీసుకోవడం పరిమితం చేయండి", "క్రమం తప్పకుండా ఆరోగ్య పరీక్షలు", "ధూమపానం మానుకోండి"]
            },
            'chatbot_samples': {
                'english': [
                    {"question": "What is diabetes?", "answer": "Diabetes is a condition where your blood sugar levels are too high due to problems with insulin."},
                    {"question": "What are diabetes symptoms?", "answer": "Common symptoms include frequent urination, excessive thirst, fatigue, and blurred vision."}
                ],
                'kannada': [
                    {"question": "ಮಧುಮೇಹ ಎಂದರೇನು?", "answer": "ಮಧುಮೇಹವು ಇನ್ಸುಲಿನ್ ಸಮಸ್ಯೆಗಳಿಂದಾಗಿ ನಿಮ್ಮ ರಕ್ತದಲ್ಲಿನ ಸಕ್ಕರೆಯ ಮಟ್ಟವು ತುಂಬಾ ಹೆಚ್ಚಿರುವ ಸ್ಥಿತಿಯಾಗಿದೆ."},
                    {"question": "ಮಧುಮೇಹದ ಲಕ್ಷಣಗಳು ಯಾವುವು?", "answer": "ಸಾಮಾನ್ಯ ಲಕ್ಷಣಗಳೆಂದರೆ ಆಗಾಗ್ಗೆ ಮೂತ್ರ ವಿಸರ್ಜನೆ, ಅತಿಯಾದ ಬಾಯಾರಿಕೆ, ಆಯಾಸ ಮತ್ತು ಮಸುಕಾದ ದೃಷ್ಟಿ."}
                ]
            },
            'voiceover_scripts': {
                'english': "Welcome to the diabetes lesson. Diabetes affects millions of people worldwide. Let's learn about this important health condition together.",
                'kannada': "ಮಧುಮೇಹ ಪಾಠಕ್ಕೆ ಸ್ವಾಗತ. ಮಧುಮೇಹವು ಪ್ರಪಂಚದಾದ್ಯಂತ ಲಕ್ಷಾಂತರ ಜನರನ್ನು ಪರಿಣಾಮಿಸುತ್ತದೆ. ಈ ಪ್ರಮುಖ ಆರೋಗ್ಯ ಸ್ಥಿತಿಯ ಬಗ್ಗೆ ಒಟ್ಟಿಗೆ ಕಲಿಯೋಣ.",
                'hindi': "मधुमेह पाठ में आपका स्वागत है। मधुमेह दुनिया भर में लाखों लोगों को प्रभावित करता है। आइए इस महत्वपूर्ण स्वास्थ्य स्थिति के बारे में एक साथ सीखते हैं।",
                'telugu': "మధుమేహ పాఠానికి స్వాగతం. మధుమేహం ప్రపంచవ్యాప్తంగా లక్షలాది మందిని ప్రభావితం చేస్తుంది. ఈ ముఖ్యమైన ఆరోగ్య పరిస్థితి గురించి కలిసి నేర్చుకుందాం."
            },
            'suggested_visuals': [
                "Human body diagram showing pancreas and insulin production",
                "Blood glucose level chart",
                "Healthy vs unhealthy food comparison",
                "Exercise demonstration videos",
                "Insulin injection technique"
            ]
        }
    }

    if disease_id not in medical_content:
        flash('Content not available for this disease.', 'danger')
        return redirect(url_for('main.lessons'))

    content = medical_content[disease_id]
    return render_template('content/generate.html', content=content, disease_id=disease_id)


# Footer Pages Routes
@bp.route('/about')
def about():
    """About page"""
    return render_template('footer/about.html')


@bp.route('/privacy')
def privacy():
    """Privacy policy page"""
    return render_template('footer/privacy.html')


@bp.route('/contact')
def contact():
    """Contact page"""
    return render_template('footer/contact.html')


@bp.route('/support')
def support():
    """Support page"""
    return render_template('footer/support.html')