<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - {{ user.get('full_name', user.username) }}</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
</head>
<body>
    <header>
        <h1>Profile Settings</h1>
        <nav>
            <ul>
                <li><a href="{{ url_for('main.dashboard') }}">Dashboard</a></li>
                <li><a href="{{ url_for('main.profile') }}">Profile</a></li>
                <li><a href="{{ url_for('main.logout') }}">Logout</a></li>
            </ul>
        </nav>
    </header>

    <main>
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <ul class="flashes">
                {% for category, message in messages %}
                    <li class="flash-{{ category }}">{{ message }}</li>
                {% endfor %}
                </ul>
            {% endif %}
        {% endwith %}

        <div class="container">
            <h2>Edit Your Profile</h2>
            
            <form method="POST" action="{{ url_for('main.profile') }}">
                {{ form.hidden_tag() }}
                
                <div class="form-section">
                    <h3>Basic Information</h3>
                    
                    <div class="form-group">
                        {{ form.full_name.label }}
                        {{ form.full_name(class="form-control") }}
                        {% for error in form.full_name.errors %}
                            <span class="error">{{ error }}</span>
                        {% endfor %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.email.label }}
                        {{ form.email(class="form-control") }}
                        {% for error in form.email.errors %}
                            <span class="error">{{ error }}</span>
                        {% endfor %}
                    </div>
                </div>

                <div class="form-section">
                    <h3>Profile Details</h3>
                    
                    <div class="form-group">
                        {{ form.bio.label }}
                        {{ form.bio(class="form-control", rows="4", placeholder="Tell us about yourself...") }}
                        {% for error in form.bio.errors %}
                            <span class="error">{{ error }}</span>
                        {% endfor %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.location.label }}
                        {{ form.location(class="form-control", placeholder="City, Country") }}
                        {% for error in form.location.errors %}
                            <span class="error">{{ error }}</span>
                        {% endfor %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.website.label }}
                        {{ form.website(class="form-control", placeholder="https://yourwebsite.com") }}
                        {% for error in form.website.errors %}
                            <span class="error">{{ error }}</span>
                        {% endfor %}
                    </div>
                </div>

                <div class="form-section">
                    <h3>Preferences</h3>
                    
                    <div class="form-group">
                        {{ form.theme.label }}
                        {{ form.theme(class="form-control") }}
                        {% for error in form.theme.errors %}
                            <span class="error">{{ error }}</span>
                        {% endfor %}
                    </div>
                    
                    <div class="form-group checkbox-group">
                        {{ form.notifications() }}
                        {{ form.notifications.label }}
                        {% for error in form.notifications.errors %}
                            <span class="error">{{ error }}</span>
                        {% endfor %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.privacy.label }}
                        {{ form.privacy(class="form-control") }}
                        {% for error in form.privacy.errors %}
                            <span class="error">{{ error }}</span>
                        {% endfor %}
                    </div>
                </div>

                <div class="form-group">
                    {{ form.submit(class="btn btn-primary") }}
                    <a href="{{ url_for('main.dashboard') }}" class="btn btn-secondary">Cancel</a>
                </div>
            </form>
        </div>
    </main>

    <footer>
        <p>&copy; 2024 Secure Flask App</p>
    </footer>
</body>
</html>
