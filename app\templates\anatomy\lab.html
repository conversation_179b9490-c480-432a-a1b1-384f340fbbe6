{% extends "base.html" %}

{% block title %}Virtual Anatomy Lab | MeduLearn{% endblock %}

{% block content %}
<div class="anatomy-lab-container">
    <!-- Modern Header -->
    <header class="lab-header">
        <div class="header-content">
            <div class="header-left">
                <a href="{{ url_for('main.dashboard') }}" class="back-link">
                    <i class="fas fa-arrow-left"></i>
                    <span>Dashboard</span>
                </a>
                <div class="header-divider"></div>
                <div class="header-title">
                    <h1>Virtual Anatomy Lab</h1>
                    <p>AI-Powered 3D Medical Education Platform</p>
                </div>
            </div>

            <div class="header-right">
                <div class="feature-badges">
                    <span class="badge badge-primary">
                        <i class="fas fa-cube"></i>
                        3D Models
                    </span>
                    <span class="badge badge-secondary">
                        <i class="fas fa-brain"></i>
                        AI Powered
                    </span>
                    <span class="badge badge-info">
                        <i class="fas fa-vr-cardboard"></i>
                        AR Ready
                    </span>
                    <span class="badge badge-warning">
                        <i class="fas fa-microphone"></i>
                        Voice Control
                    </span>
                </div>
            </div>
        </div>
    </header>

<!-- Professional Controls Section -->
<div class="section">
    <div class="section-content">
        <div class="card">
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-cogs" style="color: var(--primary-color);"></i>
                    Learning Controls
                </div>
                <div class="card-subtitle">Customize your anatomy learning experience</div>
            </div>
            <div class="card-body">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; align-items: end;">
                    <div class="form-group" style="margin-bottom: 0;">
                        <label class="form-label">
                            <i class="fas fa-language" style="margin-right: 0.5rem; color: var(--gray-500);"></i>
                            Language
                        </label>
                        <select id="anatomy-language" class="form-control">
                            <option value="english">English</option>
                            <option value="kannada">ಕನ್ನಡ (Kannada)</option>
                            <option value="hindi">हिंदी (Hindi)</option>
                            <option value="telugu">తెలుగు (Telugu)</option>
                        </select>
                    </div>

                    <div style="display: flex; gap: 0.5rem;">
                        <button id="start-audio" class="btn btn-secondary">
                            <i class="fas fa-volume-up"></i> Enable Audio
                        </button>
                        <button id="stop-audio" class="btn btn-secondary" style="display: none;">
                            <i class="fas fa-volume-mute"></i> Stop Audio
                        </button>
                    </div>

                    <button id="enable-ar" class="btn btn-outline-primary">
                        <i class="fas fa-vr-cardboard"></i> Enable AR Mode
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Professional Anatomy Systems Section -->
<div class="section">
    <div class="section-content">
        <div class="section-header">
            <h2 class="section-title">Anatomy Systems</h2>
            <p class="section-subtitle">Explore human anatomy through interactive 3D models and AI-guided learning</p>
        </div>

        <div class="grid grid-auto-fit">
            {% for system in anatomy_systems %}
            <div class="card" data-system="{{ system.id }}">
                <div class="card-header">
                    <div style="display: flex; align-items: center; gap: 1rem;">
                        <div style="width: 48px; height: 48px; border-radius: 50%; background: var(--secondary-color); display: flex; align-items: center; justify-content: center; color: white; font-size: 1.25rem;">
                            <i class="{{ system.icon }}"></i>
                        </div>
                        <div style="flex: 1;">
                            <div class="card-title" style="margin: 0;">
                                <span class="system-name"
                                    data-english="{{ system.name }}"
                                    data-kannada="{{ system.name_kannada }}"
                                    data-hindi="{{ system.name_hindi }}"
                                    data-telugu="{{ system.name_telugu }}">{{ system.name }}</span>
                            </div>
                            <div class="card-subtitle">{{ system.description }}</div>
                        </div>
                    </div>

                    <div style="display: flex; gap: 0.5rem; margin-top: 1rem;">
                        <span style="background: {% if system.difficulty.lower() == 'beginner' %}var(--success){% elif system.difficulty.lower() == 'intermediate' %}var(--warning){% else %}var(--error){% endif %}; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem; font-weight: 500;">
                            <i class="fas fa-signal"></i> {{ system.difficulty }}
                        </span>
                        <span style="background: var(--gray-100); color: var(--gray-700); padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem; font-weight: 500;">
                            <i class="fas fa-clock"></i> {{ system.duration }}
                        </span>
                    </div>
                </div>

                <div class="card-body">
                    <div style="margin-bottom: 1.5rem;">
                        <h4 style="color: var(--gray-700); margin-bottom: 0.75rem; font-size: 0.875rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.05em;">
                            <i class="fas fa-list" style="color: var(--primary-color);"></i> Key Structures
                        </h4>
                        <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                            {% for structure in system.structures %}
                            <span style="background: var(--gray-100); color: var(--gray-700); padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;">{{ structure }}</span>
                            {% endfor %}
                        </div>
                    </div>

                    <div style="margin-bottom: 1.5rem;">
                        <h4 style="color: var(--gray-700); margin-bottom: 0.75rem; font-size: 0.875rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.05em;">
                            <i class="fas fa-stethoscope" style="color: var(--secondary-color);"></i> Practice Procedures
                        </h4>
                        <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                            {% for procedure in system.procedures %}
                            <span style="background: #f0fdf4; color: var(--secondary-color); padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;">{{ procedure }}</span>
                            {% endfor %}
                        </div>
                    </div>

                    <div style="margin-bottom: 1.5rem;">
                        <h4 style="color: var(--gray-700); margin-bottom: 0.75rem; font-size: 0.875rem; font-weight: 600; text-transform: uppercase; letter-spacing: 0.05em;">
                            <i class="fas fa-brain" style="color: var(--info);"></i> AI Features
                        </h4>
                        <div style="display: grid; gap: 0.5rem;">
                            <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--gray-600); font-size: 0.875rem;">
                                <i class="fas fa-brain" style="color: var(--info);"></i>
                                <span>AI identifies weak areas</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--gray-600); font-size: 0.875rem;">
                                <i class="fas fa-route" style="color: var(--info);"></i>
                                <span>Personalized learning path</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem; color: var(--gray-600); font-size: 0.875rem;">
                                <i class="fas fa-trophy" style="color: var(--info);"></i>
                                <span>Adaptive challenges</span>
                            </div>
                        </div>
                    </div>

                    <!-- Progress Indicator -->
                    <div style="margin-bottom: 1rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                            <span style="font-size: 0.875rem; font-weight: 500; color: var(--gray-700);">Progress</span>
                            <span style="font-size: 0.875rem; color: var(--gray-600);">Not Started</span>
                        </div>
                        <div style="background: var(--gray-200); height: 6px; border-radius: 3px; overflow: hidden;">
                            <div style="background: var(--secondary-color); height: 100%; width: 0%; transition: width 0.3s ease;"></div>
                        </div>
                    </div>
                </div>

                <div class="card-footer">
                    <div style="display: grid; grid-template-columns: 1fr auto auto; gap: 0.5rem; align-items: center;">
                        <a href="{{ url_for('main.view_anatomy_system', system_id=system.id) }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-play"></i> Start 3D Exploration
                        </a>
                        <button class="btn btn-outline-primary btn-sm voice-btn" data-system="{{ system.id }}">
                            <i class="fas fa-microphone"></i>
                        </button>
                        <button class="btn btn-outline-primary btn-sm ar-btn" data-system="{{ system.id }}">
                            <i class="fas fa-vr-cardboard"></i>
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Professional Features Section -->
<div class="section" style="background: var(--gray-50);">
    <div class="section-content">
        <div class="section-header">
            <h2 class="section-title">Advanced Features</h2>
            <p class="section-subtitle">Cutting-edge technology for immersive medical education</p>
        </div>

        <div class="grid grid-cols-3">
            <div class="card">
                <div class="card-body" style="text-align: center;">
                    <div style="width: 64px; height: 64px; border-radius: 50%; background: var(--primary-color); display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; color: white; font-size: 1.5rem;">
                        <i class="fas fa-cube"></i>
                    </div>
                    <h3 style="color: var(--gray-900); margin-bottom: 0.5rem; font-size: 1.125rem; font-weight: 600;">High-Fidelity 3D Models</h3>
                    <p style="color: var(--gray-600); font-size: 0.875rem; line-height: 1.5;">Detailed, accurate anatomical structures with realistic textures and animations</p>
                </div>
            </div>

            <div class="card">
                <div class="card-body" style="text-align: center;">
                    <div style="width: 64px; height: 64px; border-radius: 50%; background: var(--secondary-color); display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; color: white; font-size: 1.5rem;">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3 style="color: var(--gray-900); margin-bottom: 0.5rem; font-size: 1.125rem; font-weight: 600;">AI-Driven Personalization</h3>
                    <p style="color: var(--gray-600); font-size: 0.875rem; line-height: 1.5;">AI identifies learning gaps and creates custom exercises and virtual patient cases</p>
                </div>
            </div>

            <div class="card">
                <div class="card-body" style="text-align: center;">
                    <div style="width: 64px; height: 64px; border-radius: 50%; background: var(--info); display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; color: white; font-size: 1.5rem;">
                        <i class="fas fa-vr-cardboard"></i>
                    </div>
                    <h3 style="color: var(--gray-900); margin-bottom: 0.5rem; font-size: 1.125rem; font-weight: 600;">Augmented Reality</h3>
                    <p style="color: var(--gray-600); font-size: 0.875rem; line-height: 1.5;">Project anatomical models onto real-world objects for enhanced spatial understanding</p>
                </div>
            </div>

            <div class="card">
                <div class="card-body" style="text-align: center;">
                    <div style="width: 64px; height: 64px; border-radius: 50%; background: var(--warning); display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; color: white; font-size: 1.5rem;">
                        <i class="fas fa-gamepad"></i>
                    </div>
                    <h3 style="color: var(--gray-900); margin-bottom: 0.5rem; font-size: 1.125rem; font-weight: 600;">Gamification</h3>
                    <p style="color: var(--gray-600); font-size: 0.875rem; line-height: 1.5;">Challenges, leaderboards, and rewards to boost engagement and motivation</p>
                </div>
            </div>

            <div class="card">
                <div class="card-body" style="text-align: center;">
                    <div style="width: 64px; height: 64px; border-radius: 50%; background: var(--accent-color); display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; color: white; font-size: 1.5rem;">
                        <i class="fas fa-microphone"></i>
                    </div>
                    <h3 style="color: var(--gray-900); margin-bottom: 0.5rem; font-size: 1.125rem; font-weight: 600;">Voice Control & NLP</h3>
                    <p style="color: var(--gray-600); font-size: 0.875rem; line-height: 1.5;">Ask questions about structures or functions using natural language</p>
                </div>
            </div>

            <div class="card">
                <div class="card-body" style="text-align: center;">
                    <div style="width: 64px; height: 64px; border-radius: 50%; background: var(--success); display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; color: white; font-size: 1.5rem;">
                        <i class="fas fa-hand-paper"></i>
                    </div>
                    <h3 style="color: var(--gray-900); margin-bottom: 0.5rem; font-size: 1.125rem; font-weight: 600;">Simulated Procedures</h3>
                    <p style="color: var(--gray-600); font-size: 0.875rem; line-height: 1.5;">Practice medical procedures with haptic feedback and real-time assessment</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Professional Learning Paths Section -->
<div class="section">
    <div class="section-content">
        <div class="section-header">
            <h2 class="section-title">AI-Generated Learning Paths</h2>
            <p class="section-subtitle">Personalized learning journeys adapted to your skill level and goals</p>
        </div>

        <div class="grid grid-cols-3">
            <div class="card" style="border-left: 4px solid var(--success);">
                <div class="card-header">
                    <div class="card-title" style="color: var(--success);">
                        <i class="fas fa-seedling"></i>
                        Beginner Path
                    </div>
                    <div class="card-subtitle">Start with basic anatomy and fundamental concepts</div>
                </div>
                <div class="card-body">
                    <div style="display: flex; align-items: center; gap: 0.5rem; font-size: 0.875rem; color: var(--gray-600);">
                        <span style="background: var(--success); color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;">Basic Structures</span>
                        <i class="fas fa-arrow-right" style="color: var(--gray-400);"></i>
                        <span style="background: var(--success); color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;">System Overview</span>
                        <i class="fas fa-arrow-right" style="color: var(--gray-400);"></i>
                        <span style="background: var(--success); color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;">Simple Procedures</span>
                    </div>
                </div>
            </div>

            <div class="card" style="border-left: 4px solid var(--warning);">
                <div class="card-header">
                    <div class="card-title" style="color: var(--warning);">
                        <i class="fas fa-chart-line"></i>
                        Intermediate Path
                    </div>
                    <div class="card-subtitle">Dive deeper into system interactions and complex procedures</div>
                </div>
                <div class="card-body">
                    <div style="display: flex; align-items: center; gap: 0.5rem; font-size: 0.875rem; color: var(--gray-600);">
                        <span style="background: var(--warning); color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;">System Integration</span>
                        <i class="fas fa-arrow-right" style="color: var(--gray-400);"></i>
                        <span style="background: var(--warning); color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;">Advanced Procedures</span>
                        <i class="fas fa-arrow-right" style="color: var(--gray-400);"></i>
                        <span style="background: var(--warning); color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;">Case Studies</span>
                    </div>
                </div>
            </div>

            <div class="card" style="border-left: 4px solid var(--error);">
                <div class="card-header">
                    <div class="card-title" style="color: var(--error);">
                        <i class="fas fa-trophy"></i>
                        Advanced Path
                    </div>
                    <div class="card-subtitle">Master complex anatomy with specialized medical procedures</div>
                </div>
                <div class="card-body">
                    <div style="display: flex; align-items: center; gap: 0.5rem; font-size: 0.875rem; color: var(--gray-600);">
                        <span style="background: var(--error); color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;">Specialized Systems</span>
                        <i class="fas fa-arrow-right" style="color: var(--gray-400);"></i>
                        <span style="background: var(--error); color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;">Expert Procedures</span>
                        <i class="fas fa-arrow-right" style="color: var(--gray-400);"></i>
                        <span style="background: var(--error); color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem;">Research Cases</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<script>
let currentLanguage = 'english';
let audioEnabled = false;
let currentAudio = null;

// Language switching
document.getElementById('anatomy-language').addEventListener('change', function() {
    currentLanguage = this.value;
    updateSystemNames();
});

function updateSystemNames() {
    const systemNames = document.querySelectorAll('.system-name');
    systemNames.forEach(name => {
        const text = name.dataset[currentLanguage];
        if (text) {
            name.textContent = text;
        }
    });
}

// Audio controls
document.getElementById('start-audio').addEventListener('click', function() {
    audioEnabled = true;
    this.style.display = 'none';
    document.getElementById('stop-audio').style.display = 'inline-block';
});

document.getElementById('stop-audio').addEventListener('click', function() {
    audioEnabled = false;
    if (currentAudio) {
        speechSynthesis.cancel();
        currentAudio = null;
    }
    this.style.display = 'none';
    document.getElementById('start-audio').style.display = 'inline-block';
});

// Voice tour functionality
document.querySelectorAll('.voice-btn').forEach(button => {
    button.addEventListener('click', function() {
        if (!audioEnabled) {
            alert('Please enable audio first');
            return;
        }
        
        const systemId = this.dataset.system;
        startVoiceTour(systemId);
    });
});

function startVoiceTour(systemId) {
    const tours = {
        'cardiovascular': 'Welcome to the cardiovascular system tour. The heart pumps blood through arteries, veins, and capillaries to deliver oxygen and nutrients throughout your body.',
        'respiratory': 'Welcome to the respiratory system. Your lungs take in oxygen and remove carbon dioxide through the process of breathing.',
        'nervous': 'Welcome to the nervous system. The brain and spinal cord control all body functions through electrical signals.',
        'musculoskeletal': 'Welcome to the musculoskeletal system. Bones provide structure while muscles enable movement through joints and ligaments.'
    };
    
    const text = tours[systemId] || 'Welcome to the anatomy tour.';
    const utterance = new SpeechSynthesisUtterance(text);
    
    const langCodes = {
        'english': 'en-US',
        'kannada': 'kn-IN',
        'hindi': 'hi-IN',
        'telugu': 'te-IN'
    };
    
    utterance.lang = langCodes[currentLanguage] || 'en-US';
    currentAudio = utterance;
    speechSynthesis.speak(utterance);
}

// AR Mode simulation
document.querySelectorAll('.ar-btn').forEach(button => {
    button.addEventListener('click', function() {
        alert('AR Mode would activate camera and overlay 3D anatomy models on real-world objects. This feature requires WebXR API support.');
    });
});

document.getElementById('enable-ar').addEventListener('click', function() {
    alert('AR Mode: This would activate your device camera and project anatomical models onto real-world surfaces for enhanced spatial learning.');
});

// Simulate progress loading
document.addEventListener('DOMContentLoaded', function() {
    // Simulate some progress for demo
    const progressBars = document.querySelectorAll('.progress-fill');
    const progressTexts = document.querySelectorAll('.progress-text');
    
    progressBars.forEach((bar, index) => {
        const progress = [25, 60, 0, 40][index] || 0;
        const text = ['25% Complete', '60% Complete', 'Not Started', '40% Complete'][index] || 'Not Started';
        
        setTimeout(() => {
            bar.style.width = progress + '%';
            progressTexts[index].textContent = text;
        }, 500 + index * 200);
    });
});
</script>

<style>
/* Modern Anatomy Lab Styles */
.anatomy-lab-container {
    min-height: 100vh;
    background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
    padding: 0;
    margin: 0;
}

.lab-header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-primary);
    padding: 2rem 0;
    position: sticky;
    top: 0;
    z-index: 100;
    box-shadow: var(--shadow-sm);
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.back-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: var(--bg-secondary);
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 500;
    transition: all 0.2s ease;
}

.back-link:hover {
    background: var(--bg-tertiary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
    color: var(--text-primary);
    text-decoration: none;
}

.header-divider {
    width: 1px;
    height: 40px;
    background: var(--border-primary);
}

.header-title h1 {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0 0 0.5rem 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.header-title p {
    font-size: 1rem;
    color: var(--text-secondary);
    margin: 0;
    font-weight: 500;
}

.feature-badges {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.badge {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    color: white;
    transition: all 0.2s ease;
}

.badge:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.badge-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.badge-secondary {
    background: linear-gradient(135deg, var(--secondary-color), var(--success));
}

.badge-info {
    background: linear-gradient(135deg, var(--info), #06b6d4);
}

.badge-warning {
    background: linear-gradient(135deg, var(--warning), #f59e0b);
}

/* Override existing section styles for full width */
.section {
    padding: 3rem 0;
    margin: 0;
    width: 100%;
}

.section-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Enhanced card styles */
.card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid var(--border-primary);
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
    border-color: var(--border-accent);
}

.card:hover::before {
    opacity: 1;
}

/* Grid improvements */
.grid {
    display: grid;
    gap: 2rem;
}

.grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}

.grid-cols-3 {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

/* Button enhancements */
.btn {
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.2s ease;
    border: none;
    cursor: pointer;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.btn-secondary {
    background: linear-gradient(135deg, var(--secondary-color), var(--success));
}

.btn-outline-primary {
    background: transparent;
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
}

/* Responsive design */
@media (max-width: 1200px) {
    .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 1.5rem;
    }

    .header-left {
        width: 100%;
        justify-content: space-between;
    }

    .feature-badges {
        justify-content: center;
        width: 100%;
    }

    .grid-auto-fit {
        grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    }
}

@media (max-width: 768px) {
    .lab-header {
        padding: 1.5rem 0;
    }

    .header-content {
        padding: 0 1rem;
    }

    .header-left {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .header-divider {
        display: none;
    }

    .header-title h1 {
        font-size: 1.5rem;
    }

    .feature-badges {
        gap: 0.5rem;
    }

    .badge {
        padding: 0.4rem 0.8rem;
        font-size: 0.8rem;
    }

    .section-content {
        padding: 0 1rem;
    }

    .grid-auto-fit {
        grid-template-columns: 1fr;
    }

    .grid-cols-3 {
        grid-template-columns: 1fr;
    }

    .card {
        border-radius: 16px;
    }
}

@media (max-width: 480px) {
    .back-link span {
        display: none;
    }

    .feature-badges {
        flex-direction: column;
        width: 100%;
    }

    .badge {
        justify-content: center;
    }
}
</style>
{% endblock %}
