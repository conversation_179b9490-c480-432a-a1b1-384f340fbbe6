#!/usr/bin/env python3
"""
Database verification script for student_app
This script verifies the MongoDB connection and shows database information
"""

from pymongo import MongoClient
import sys

def verify_student_app_database():
    """Verify the student_app database connection and contents"""
    try:
        # Connect to MongoDB
        client = MongoClient('mongodb://localhost:27017/')
        
        print("Connecting to MongoDB...")
        
        # Check if MongoDB is running
        client.admin.command('ping')
        print("✓ MongoDB connection successful")
        
        # Access the student_app database
        db = client['student_app']
        
        # Get database list
        db_list = client.list_database_names()
        
        print("\n" + "="*50)
        print("DATABASE INFORMATION")
        print("="*50)
        print(f"MongoDB Server: {client.address}")
        print(f"All databases: {db_list}")
        print(f"student_app exists: {'student_app' in db_list}")
        
        if 'student_app' in db_list:
            collections = db.list_collection_names()
            print(f"Collections in student_app: {collections}")
            
            # Show collection details
            print("\n" + "-"*30)
            print("COLLECTION DETAILS")
            print("-"*30)
            
            for collection_name in collections:
                collection = db[collection_name]
                count = collection.count_documents({})
                indexes = list(collection.list_indexes())
                index_names = [idx['name'] for idx in indexes]
                
                print(f"\n📁 {collection_name}:")
                print(f"   Documents: {count}")
                print(f"   Indexes: {index_names}")
            
            # Show test user if exists
            if 'users' in collections:
                test_user = db.users.find_one({"username": "testuser"})
                if test_user:
                    print(f"\n👤 Test user found: {test_user['username']}")
                    print("   Password: testpass123")
                else:
                    print("\n👤 No test user found")
        else:
            print("❌ student_app database not found!")
            return False
        
        print("\n" + "="*50)
        print("✅ Database verification completed!")
        print("="*50)
        
        # Close connection
        client.close()
        return True
        
    except Exception as e:
        print(f"❌ Error verifying database: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("Student App Database Verifier")
    print("="*35)
    
    success = verify_student_app_database()
    
    if success:
        print("\n✅ Database verification successful!")
        sys.exit(0)
    else:
        print("\n❌ Database verification failed!")
        sys.exit(1)
