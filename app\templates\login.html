{% extends "base.html" %}

{% block title %}Login | MeduLearn - AI-Powered Medical Education{% endblock %}

{% block content %}
<div style="min-height: calc(100vh - 80px); display: flex; align-items: center; justify-content: center; padding: 2rem 1rem;">
    <div class="container" style="max-width: 480px; margin: 0;">
        <div style="text-align: center; margin-bottom: 2.5rem;">
            <div style="display: inline-flex; align-items: center; justify-content: center; width: 80px; height: 80px; background: var(--primary-color); border-radius: 50%; margin-bottom: 1.5rem; box-shadow: var(--shadow-lg);">
                <i class="fas fa-user-md" style="font-size: 2rem; color: var(--text-inverse);"></i>
            </div>
            <h1 style="font-size: 2.2rem; font-weight: 700; color: var(--primary-color); margin-bottom: 0.75rem;">Welcome to MeduLearn</h1>
            <p style="color: var(--text-secondary); font-size: 1.1rem; font-weight: 400;">Sign in to continue your medical education journey</p>
        </div>

        <form method="POST" action="{{ url_for('main.login') }}">
            {{ form.hidden_tag() }}

            <div class="form-group">
                <label class="form-label" for="username">
                    <i class="fas fa-user" style="margin-right: 0.75rem; color: var(--primary-color);"></i>
                    Username
                </label>
                {{ form.username(class="form-control", id="username", placeholder="Enter your username") }}
                {% for error in form.username.errors %}
                    <div class="error">
                        {{ error }}
                    </div>
                {% endfor %}
            </div>

            <div class="form-group">
                <label class="form-label" for="password">
                    <i class="fas fa-lock" style="margin-right: 0.75rem; color: var(--primary-color);"></i>
                    Password
                </label>
                <div style="position: relative;">
                    {{ form.password(class="form-control", id="password", placeholder="Enter your password") }}
                    <button type="button" onclick="togglePassword()" style="position: absolute; right: 1rem; top: 50%; transform: translateY(-50%); background: none; border: none; color: var(--text-secondary); cursor: pointer; transition: all 0.3s ease; padding: 0.5rem;">
                        <i class="fas fa-eye" id="password-eye"></i>
                    </button>
                </div>
                {% for error in form.password.errors %}
                    <div class="error">
                        {{ error }}
                    </div>
                {% endfor %}
            </div>

            <div class="form-group">
                {{ form.submit(class="btn btn-primary", style="width: 100%;") }}
            </div>
        </form>

        <div style="text-align: center; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid var(--border-secondary);">
            <p style="color: var(--text-secondary); font-size: 0.95rem;">
                Don't have an account?
                <a href="{{ url_for('main.register') }}" style="color: var(--primary-color); text-decoration: none; font-weight: 600; transition: all 0.3s ease;">
                    Create one here <i class="fas fa-arrow-right" style="margin-left: 0.5rem;"></i>
                </a>
            </p>
        </div>
    </div>
</div>

<script>
function togglePassword() {
    const field = document.getElementById('password');
    const eye = document.getElementById('password-eye');

    if (field.type === 'password') {
        field.type = 'text';
        eye.className = 'fas fa-eye-slash';
    } else {
        field.type = 'password';
        eye.className = 'fas fa-eye';
    }
}
</script>
{% endblock %}