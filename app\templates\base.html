<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}EngiLearn - AI-Powered Engineering Education{% endblock %}</title>
    <meta name="description" content="Professional AI-powered multilingual engineering education platform for students and professionals">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ url_for('static', filename='styles.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link rel="icon" type="image/x-icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>⚙️</text></svg>">
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="{{ url_for('main.index') if session.user_id else url_for('main.login') }}">
                    <i class="fas fa-cogs"></i>
                    <span>EngiLearn</span>
                </a>
            </div>

            <div class="nav-menu" id="nav-menu">
                {% if session.user_id %}
                    <a href="{{ url_for('main.chatbot') }}" class="nav-link">
                        <i class="fas fa-robot"></i>
                        <span>AI Assistant</span>
                    </a>
                    <div class="nav-dropdown">
                        <a href="#" class="nav-link dropdown-toggle">
                            <i class="fas fa-user-circle"></i>
                            <span>{{ session.username }}</span>
                            <i class="fas fa-chevron-down"></i>
                        </a>
                        <div class="dropdown-menu">
                            <a href="{{ url_for('main.dashboard') }}" class="dropdown-item">
                                <i class="fas fa-home"></i>
                                Dashboard
                            </a>
                            <a href="{{ url_for('main.profile') }}" class="dropdown-item">
                                <i class="fas fa-user-edit"></i>
                                Profile Settings
                            </a>
                            <div class="dropdown-divider"></div>
                            <a href="{{ url_for('main.logout') }}" class="dropdown-item logout">
                                <i class="fas fa-sign-out-alt"></i>
                                Logout
                            </a>
                        </div>
                    </div>
                {% else %}
                    <a href="{{ url_for('main.login') }}" class="nav-link">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>Login</span>
                    </a>
                    <a href="{{ url_for('main.register') }}" class="nav-link nav-cta">
                        <i class="fas fa-user-plus"></i>
                        <span>Register</span>
                    </a>
                {% endif %}
            </div>

            <div class="nav-toggle" id="nav-toggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <main class="main-content">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                <div class="flash-messages">
                {% for category, message in messages %}
                    <div class="flash-message flash-{{ category }}">
                        <i class="fas fa-{{ 'check-circle' if category == 'success' else 'exclamation-triangle' }}"></i>
                        {{ message }}
                        <button class="flash-close" onclick="this.parentElement.remove()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                {% endfor %}
                </div>
            {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </main>

    <!-- Compact Modern Footer -->
    <footer class="footer">
        <div class="footer-content">
            <div class="footer-brand">
                <span class="footer-logo">
                    <i class="fas fa-cogs"></i>
                    EngiLearn
                </span>
                <span class="footer-tagline">Student Community Platform</span>
            </div>

            <div class="footer-links">
                <a href="{{ url_for('main.about') }}" class="footer-link">About</a>
                <a href="{{ url_for('main.privacy') }}" class="footer-link">Privacy</a>
                <a href="{{ url_for('main.contact') }}" class="footer-link">Contact</a>
                <a href="{{ url_for('main.support') }}" class="footer-link">Support</a>
            </div>

            <div class="footer-copyright">
                <span>&copy; 2024 EngiLearn</span>
            </div>
        </div>
    </footer>

    <script>
        // Mobile menu toggle
        const navToggle = document.getElementById('nav-toggle');
        const navMenu = document.getElementById('nav-menu');

        navToggle.addEventListener('click', () => {
            navMenu.classList.toggle('active');
            navToggle.classList.toggle('active');
        });

        // Dropdown functionality
        document.addEventListener('click', (e) => {
            const dropdown = document.querySelector('.nav-dropdown');
            if (!dropdown.contains(e.target)) {
                dropdown.classList.remove('active');
            }
        });

        document.querySelector('.dropdown-toggle').addEventListener('click', (e) => {
            e.preventDefault();
            document.querySelector('.nav-dropdown').classList.toggle('active');
        });

        // Auto-hide flash messages
        setTimeout(() => {
            const flashMessages = document.querySelectorAll('.flash-message');
            flashMessages.forEach(msg => {
                msg.style.opacity = '0';
                msg.style.transform = 'translateY(-20px)';
                setTimeout(() => msg.remove(), 300);
            });
        }, 5000);
    </script>
</body>
</html>