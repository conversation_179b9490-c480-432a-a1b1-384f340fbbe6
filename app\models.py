from datetime import datetime, timezone
from bson import ObjectId

class User:
    def __init__(self, username, password, email=None, full_name=None):
        self.username = username
        self.password = password
        self.email = email
        self.full_name = full_name
        self.created_at = datetime.now(timezone.utc)
        self.last_login = None
        self.profile_data = {
            'bio': '',
            'location': '',
            'website': '',
            'profile_picture': '',
            'preferences': {
                'theme': 'light',
                'notifications': True,
                'privacy': 'public',
                'preferred_language': 'english'
            }
        }


    def to_dict(self):
        return {
            'username': self.username,
            'password': self.password,
            'email': self.email,
            'full_name': self.full_name,
            'created_at': self.created_at,
            'last_login': self.last_login,
            'profile_data': self.profile_data
        }

    @staticmethod
    def from_dict(data):
        user = User(
            username=data.get('username'),
            password=data.get('password'),
            email=data.get('email'),
            full_name=data.get('full_name')
        )
        user.created_at = data.get('created_at', datetime.now(timezone.utc))
        user.last_login = data.get('last_login')
        user.profile_data = data.get('profile_data', user.profile_data)
        return user


# Additional models can be added here if needed in the future