from flask_wtf import FlaskForm
from flask_wtf.file import <PERSON><PERSON>ield, FileAllowed
from wtforms import <PERSON><PERSON><PERSON>, PasswordField, SubmitField, EmailField, TextAreaField, SelectField, BooleanField
from wtforms.validators import DataRequired, Length, EqualTo, Email, Optional

class RegistrationForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired(), Length(min=2, max=20)])
    email = EmailField('Email', validators=[DataRequired(), Email()])
    full_name = StringField('Full Name', validators=[DataRequired(), Length(min=2, max=50)])
    password = PasswordField('Password', validators=[DataRequired(), Length(min=6)])
    confirm_password = PasswordField('Confirm Password', validators=[DataRequired(), EqualTo('password')])
    submit = SubmitField('Sign Up')

class LoginForm(FlaskForm):
    username = <PERSON><PERSON><PERSON>('Username', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired()])
    submit = SubmitField('Login')

class ProfileForm(FlaskForm):
    full_name = StringField('Full Name', validators=[DataRequired(), Length(min=2, max=50)])
    email = EmailField('Email', validators=[DataRequired(), Email()])
    bio = TextAreaField('Bio', validators=[Optional(), Length(max=500)])
    location = StringField('Location', validators=[Optional(), Length(max=100)])
    website = StringField('Website', validators=[Optional(), Length(max=200)])
    profile_picture = FileField('Profile Picture', validators=[Optional(), FileAllowed(['jpg', 'png', 'jpeg', 'gif', 'webp'], 'Images only!')])
    theme = SelectField('Theme', choices=[('light', 'Light'), ('dark', 'Dark')], default='light')
    notifications = BooleanField('Enable Notifications', default=True)
    privacy = SelectField('Profile Privacy', choices=[('public', 'Public'), ('private', 'Private')], default='public')
    preferred_language = SelectField('Preferred Language',
                                   choices=[('english', 'English'),
                                           ('kannada', 'ಕನ್ನಡ (Kannada)'),
                                           ('hindi', 'हिंदी (Hindi)'),
                                           ('telugu', 'తెలుగు (Telugu)')],
                                   default='english')
    submit = SubmitField('Update Profile')


# Medical Education Forms can be added here if needed in the future