{% extends "base.html" %}

{% block title %}About | MeduLearn{% endblock %}

{% block content %}
<!-- Professional Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="breadcrumb">
            <a href="{{ url_for('main.dashboard') }}">
                <i class="fas fa-home"></i> Dashboard
            </a>
            <span class="breadcrumb-separator">/</span>
            <span>About</span>
        </div>
        <h1 class="page-title">About MeduLearn</h1>
        <p class="page-subtitle">
            Empowering rural communities with AI-powered multilingual medical education
        </p>
    </div>
</div>

<!-- About Content -->
<div class="section">
    <div class="section-content">
        <div class="grid grid-cols-2">
            <!-- Mission Section -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-bullseye" style="color: var(--primary-color);"></i>
                        Our Mission
                    </div>
                </div>
                <div class="card-body">
                    <p style="color: var(--gray-600); line-height: 1.6;">
                        MeduLearn is dedicated to democratizing medical education by providing AI-powered, 
                        multilingual learning experiences specifically designed for rural communities in India. 
                        We bridge the gap between advanced medical knowledge and accessibility through innovative 
                        technology and culturally sensitive content delivery.
                    </p>
                </div>
            </div>

            <!-- Vision Section -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-eye" style="color: var(--secondary-color);"></i>
                        Our Vision
                    </div>
                </div>
                <div class="card-body">
                    <p style="color: var(--gray-600); line-height: 1.6;">
                        To create a world where quality medical education is accessible to everyone, regardless 
                        of their location, language, or economic background. We envision empowered communities 
                        with improved health outcomes through comprehensive medical literacy and practical 
                        healthcare knowledge.
                    </p>
                </div>
            </div>
        </div>

        <!-- Key Features Section -->
        <div class="section-header" style="margin-top: 3rem;">
            <h2 class="section-title">What Makes MeduLearn Special</h2>
            <p class="section-subtitle">Advanced technology meets cultural sensitivity</p>
        </div>

        <div class="grid grid-cols-3">
            <div class="card">
                <div class="card-body" style="text-align: center;">
                    <div style="width: 64px; height: 64px; border-radius: 50%; background: var(--primary-color); display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; color: white; font-size: 1.5rem;">
                        <i class="fas fa-language"></i>
                    </div>
                    <h3 style="color: var(--gray-900); margin-bottom: 0.5rem; font-size: 1.125rem; font-weight: 600;">Multilingual Support</h3>
                    <p style="color: var(--gray-600); font-size: 0.875rem; line-height: 1.5;">
                        Content available in Kannada, Hindi, Telugu, and English to serve diverse communities
                    </p>
                </div>
            </div>

            <div class="card">
                <div class="card-body" style="text-align: center;">
                    <div style="width: 64px; height: 64px; border-radius: 50%; background: var(--secondary-color); display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; color: white; font-size: 1.5rem;">
                        <i class="fas fa-brain"></i>
                    </div>
                    <h3 style="color: var(--gray-900); margin-bottom: 0.5rem; font-size: 1.125rem; font-weight: 600;">AI-Powered Learning</h3>
                    <p style="color: var(--gray-600); font-size: 0.875rem; line-height: 1.5;">
                        Personalized learning paths and adaptive content based on individual progress and needs
                    </p>
                </div>
            </div>

            <div class="card">
                <div class="card-body" style="text-align: center;">
                    <div style="width: 64px; height: 64px; border-radius: 50%; background: var(--info); display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; color: white; font-size: 1.5rem;">
                        <i class="fas fa-cube"></i>
                    </div>
                    <h3 style="color: var(--gray-900); margin-bottom: 0.5rem; font-size: 1.125rem; font-weight: 600;">3D Anatomy Lab</h3>
                    <p style="color: var(--gray-600); font-size: 0.875rem; line-height: 1.5;">
                        Interactive 3D models with AR support for immersive anatomy learning experiences
                    </p>
                </div>
            </div>

            <div class="card">
                <div class="card-body" style="text-align: center;">
                    <div style="width: 64px; height: 64px; border-radius: 50%; background: var(--warning); display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; color: white; font-size: 1.5rem;">
                        <i class="fas fa-microphone"></i>
                    </div>
                    <h3 style="color: var(--gray-900); margin-bottom: 0.5rem; font-size: 1.125rem; font-weight: 600;">Voice Control</h3>
                    <p style="color: var(--gray-600); font-size: 0.875rem; line-height: 1.5;">
                        Natural language processing for voice-controlled learning and accessibility
                    </p>
                </div>
            </div>

            <div class="card">
                <div class="card-body" style="text-align: center;">
                    <div style="width: 64px; height: 64px; border-radius: 50%; background: var(--accent-color); display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; color: white; font-size: 1.5rem;">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 style="color: var(--gray-900); margin-bottom: 0.5rem; font-size: 1.125rem; font-weight: 600;">Community Focus</h3>
                    <p style="color: var(--gray-600); font-size: 0.875rem; line-height: 1.5;">
                        Designed specifically for rural communities with culturally appropriate content
                    </p>
                </div>
            </div>

            <div class="card">
                <div class="card-body" style="text-align: center;">
                    <div style="width: 64px; height: 64px; border-radius: 50%; background: var(--success); display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; color: white; font-size: 1.5rem;">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <h3 style="color: var(--gray-900); margin-bottom: 0.5rem; font-size: 1.125rem; font-weight: 600;">Mobile Accessible</h3>
                    <p style="color: var(--gray-600); font-size: 0.875rem; line-height: 1.5;">
                        Optimized for mobile devices to ensure accessibility in remote areas
                    </p>
                </div>
            </div>
        </div>

        <!-- Impact Section -->
        <div class="section-header" style="margin-top: 3rem;">
            <h2 class="section-title">Our Impact</h2>
            <p class="section-subtitle">Making a difference in healthcare education</p>
        </div>

        <div class="card">
            <div class="card-body">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 2rem; text-align: center;">
                    <div>
                        <div style="font-size: 2.5rem; font-weight: 700; color: var(--primary-color); margin-bottom: 0.5rem;">1000+</div>
                        <div style="color: var(--gray-600); font-weight: 500;">Active Learners</div>
                    </div>
                    <div>
                        <div style="font-size: 2.5rem; font-weight: 700; color: var(--secondary-color); margin-bottom: 0.5rem;">4</div>
                        <div style="color: var(--gray-600); font-weight: 500;">Languages Supported</div>
                    </div>
                    <div>
                        <div style="font-size: 2.5rem; font-weight: 700; color: var(--info); margin-bottom: 0.5rem;">50+</div>
                        <div style="color: var(--gray-600); font-weight: 500;">Medical Topics</div>
                    </div>
                    <div>
                        <div style="font-size: 2.5rem; font-weight: 700; color: var(--warning); margin-bottom: 0.5rem;">95%</div>
                        <div style="color: var(--gray-600); font-weight: 500;">User Satisfaction</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Team Section -->
        <div class="section-header" style="margin-top: 3rem;">
            <h2 class="section-title">Our Commitment</h2>
        </div>

        <div class="card">
            <div class="card-body">
                <p style="color: var(--gray-600); line-height: 1.8; font-size: 1.125rem; text-align: center;">
                    MeduLearn is committed to continuous innovation in medical education technology. 
                    We work closely with healthcare professionals, educators, and community leaders 
                    to ensure our platform meets the real-world needs of rural healthcare workers 
                    and community health advocates. Together, we're building a healthier, more 
                    informed world, one community at a time.
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
