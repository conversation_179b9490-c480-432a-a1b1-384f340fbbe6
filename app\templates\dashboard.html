{% extends "base.html" %}

{% block title %}Dashboard - {{ user.get('full_name', user.username) }} | EngiLearn{% endblock %}

{% block content %}
<!-- Professional Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="breadcrumb">
            <a href="{{ url_for('main.dashboard') }}">
                <i class="fas fa-home"></i> Dashboard
            </a>
        </div>
        <h1 class="page-title">
            Welcome back, {{ user.get('full_name', user.username) }}
        </h1>
        <p class="page-subtitle">
            Welcome to your student dashboard with AI-powered assistance
        </p>
    </div>
</div>

<!-- Professional Quick Access Section -->
<div class="section">
    <div class="section-content">
        <div class="section-header">
            <h2 class="section-title">Quick Access</h2>
            <p class="section-subtitle">Access your tools and connect with the community</p>
        </div>

        <div class="grid grid-auto-fit">
            <a href="{{ url_for('main.chatbot') }}" class="card" style="text-decoration: none; color: inherit;">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-robot" style="color: var(--info);"></i>
                        AI Assistant
                    </div>
                    <div class="card-subtitle">AI ಸಹಾಯಕ | AI सहायक | AI సహాయకుడు</div>
                </div>
                <div class="card-body">
                    <p>Get instant answers and guidance from our AI assistant in your preferred language.</p>
                </div>
                <div class="card-footer">
                    <span style="color: var(--text-secondary); font-size: 0.875rem;">Ask Questions</span>
                    <i class="fas fa-arrow-right" style="color: var(--info);"></i>
                </div>
            </a>

            <a href="{{ url_for('main.forum') }}" class="card" style="text-decoration: none; color: inherit;">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-users" style="color: var(--primary-color);"></i>
                        Community Forum
                    </div>
                    <div class="card-subtitle">ಸಮುದಾಯ ವೇದಿಕೆ | समुदाय मंच | కమ్యూనిటీ ఫోరమ్</div>
                </div>
                <div class="card-body">
                    <p>Connect with other students, share experiences, and participate in discussions.</p>
                </div>
                <div class="card-footer">
                    <span style="color: var(--text-secondary); font-size: 0.875rem;">Join Discussion</span>
                    <i class="fas fa-arrow-right" style="color: var(--primary-color);"></i>
                </div>
            </a>

            <a href="{{ url_for('main.view_blogs') }}" class="card" style="text-decoration: none; color: inherit;">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-blog" style="color: var(--secondary-color);"></i>
                        Student Blogs
                    </div>
                    <div class="card-subtitle">ವಿದ್ಯಾರ್ಥಿ ಬ್ಲಾಗ್‌ಗಳು | छात्र ब्लॉग | విద్యార్థి బ్లాగులు</div>
                </div>
                <div class="card-body">
                    <p>Read and share stories, experiences, and insights from the student community.</p>
                </div>
                <div class="card-footer">
                    <span style="color: var(--text-secondary); font-size: 0.875rem;">Read Stories</span>
                    <i class="fas fa-arrow-right" style="color: var(--secondary-color);"></i>
                </div>
            </a>
        </div>
    </div>
</div>

<!-- Professional Dashboard Content -->
<div class="section">
    <div class="section-content">
        <div class="grid grid-cols-3">
            <!-- User Profile Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-user-circle" style="color: var(--primary-color);"></i>
                        Profile Overview
                    </div>
                </div>
                <div class="card-body">
                    <div style="display: flex; flex-direction: column; gap: 0.75rem;">
                        <div><strong>Username:</strong> {{ user.username }}</div>
                        <div><strong>Full Name:</strong> {{ user.get('full_name', 'Not set') }}</div>
                        <div><strong>Email:</strong> {{ user.get('email', 'Not set') }}</div>
                        <div><strong>Bio:</strong> {{ user.get('profile_data', {}).get('bio', 'No bio yet') }}</div>
                        <div><strong>Location:</strong> {{ user.get('profile_data', {}).get('location', 'Not set') }}</div>
                    </div>
                    <div style="margin-top: 1rem;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
                            <span><strong>Profile Completion:</strong></span>
                            <span>{{ stats.profile_completion }}%</span>
                        </div>
                        <div style="background: var(--bg-secondary); height: 8px; border-radius: 4px; overflow: hidden;">
                            <div style="background: var(--primary-color); height: 100%; width: {{ stats.profile_completion }}%; transition: width 0.3s ease;"></div>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <a href="{{ url_for('main.profile') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-edit"></i> Edit Profile
                    </a>
                </div>
            </div>

            <!-- Statistics Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-chart-bar" style="color: var(--secondary-color);"></i>
                        Account Statistics
                    </div>
                </div>
                <div class="card-body">
                    <div style="display: grid; gap: 1rem;">
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fas fa-calendar-alt" style="color: var(--text-secondary);"></i>
                            <div>
                                <div style="font-weight: 500;">Member Since</div>
                                <div style="font-size: 0.875rem; color: var(--text-secondary);">{{ stats.member_since.strftime('%B %d, %Y') if stats.member_since != 'Unknown' else 'Unknown' }}</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fas fa-clock" style="color: var(--text-secondary);"></i>
                            <div>
                                <div style="font-weight: 500;">Last Login</div>
                                <div style="font-size: 0.875rem; color: var(--text-secondary);">{{ stats.last_login.strftime('%B %d, %Y at %I:%M %p') if stats.last_login != 'Never' else 'Never' }}</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fas fa-file-alt" style="color: var(--text-secondary);"></i>
                            <div>
                                <div style="font-weight: 500;">Your Posts</div>
                                <div style="font-size: 1.25rem; font-weight: 600; color: var(--primary-color);">{{ stats.user_posts }}</div>
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fas fa-comments" style="color: var(--text-secondary);"></i>
                            <div>
                                <div style="font-weight: 500;">Your Comments</div>
                                <div style="font-size: 1.25rem; font-weight: 600; color: var(--secondary-color);">{{ stats.user_comments }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity Card -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-history" style="color: var(--info);"></i>
                        Recent Activity
                    </div>
                </div>
                <div class="card-body">
                    <div style="display: flex; flex-direction: column; gap: 1rem;">
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fas fa-sign-in-alt" style="color: var(--success);"></i>
                            <div>
                                <div style="font-weight: 500;">Logged in</div>
                                <div style="font-size: 0.875rem; color: var(--text-secondary);">{{ stats.last_login.strftime('%B %d at %I:%M %p') if stats.last_login != 'Never' else 'Welcome!' }}</div>
                            </div>
                        </div>
                        {% if stats.user_posts > 0 %}
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fas fa-file-alt" style="color: var(--primary-color);"></i>
                            <div>
                                <div style="font-weight: 500;">{{ stats.user_posts }} posts created</div>
                                <div style="font-size: 0.875rem; color: var(--text-secondary);">Keep sharing!</div>
                            </div>
                        </div>
                        {% endif %}
                        {% if stats.user_comments > 0 %}
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fas fa-comment" style="color: var(--secondary-color);"></i>
                            <div>
                                <div style="font-weight: 500;">{{ stats.user_comments }} comments made</div>
                                <div style="font-size: 0.875rem; color: var(--text-secondary);">Great engagement!</div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
