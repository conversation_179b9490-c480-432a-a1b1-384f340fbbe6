{% extends "base.html" %}

{% block title %}Community Forum - Secure Flask App{% endblock %}

{% block content %}
<div class="forum-container">
    <div class="forum-header">
        <div class="forum-title">
            <h1><i class="fas fa-comments"></i> Community Forum</h1>
            <p>Share your thoughts, images, and connect with the community</p>
        </div>
        {% if session.user_id %}
        <a href="{{ url_for('main.create_post') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            Create New Post
        </a>
        {% endif %}
    </div>

    <div class="forum-stats">
        <div class="stat-card">
            <i class="fas fa-file-alt"></i>
            <div>
                <h3>{{ total_posts }}</h3>
                <p>Total Posts</p>
            </div>
        </div>
        <div class="stat-card">
            <i class="fas fa-users"></i>
            <div>
                <h3>{{ total_users }}</h3>
                <p>Community Members</p>
            </div>
        </div>
        <div class="stat-card">
            <i class="fas fa-comments"></i>
            <div>
                <h3>{{ total_comments }}</h3>
                <p>Total Comments</p>
            </div>
        </div>
    </div>

    <div class="forum-content">
        <div class="posts-section">
            <div class="section-header">
                <h2>Recent Posts</h2>
                <div class="sort-options">
                    <a href="{{ url_for('main.forum', sort='recent') }}" class="sort-btn {{ 'active' if sort_by == 'recent' else '' }}">
                        <i class="fas fa-clock"></i> Recent
                    </a>
                    <a href="{{ url_for('main.forum', sort='popular') }}" class="sort-btn {{ 'active' if sort_by == 'popular' else '' }}">
                        <i class="fas fa-fire"></i> Popular
                    </a>
                </div>
            </div>

            {% if posts %}
                <div class="posts-grid">
                    {% for post in posts %}
                    <article class="post-card">
                        <div class="post-header">
                            <div class="author-info">
                                <div class="author-avatar">
                                    {% if post.get('author_profile_picture') %}
                                        <img src="{{ url_for('static', filename='uploads/' + post.author_profile_picture) }}" alt="{{ post.author_username }}">
                                    {% else %}
                                        <i class="fas fa-user-circle"></i>
                                    {% endif %}
                                </div>
                                <div class="author-details">
                                    <h4>{{ post.author_username }}</h4>
                                    <time datetime="{{ post.created_at.isoformat() }}">
                                        {{ post.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                                    </time>
                                </div>
                            </div>
                            <div class="post-stats">
                                <span class="views">
                                    <i class="fas fa-eye"></i>
                                    {{ post.views }}
                                </span>
                            </div>
                        </div>

                        {% if post.image_filename %}
                        <div class="post-image">
                            <img src="{{ url_for('static', filename='uploads/' + post.image_filename) }}" alt="{{ post.title }}">
                        </div>
                        {% endif %}

                        <div class="post-content">
                            <h3><a href="{{ url_for('main.view_post', post_id=post._id) }}">{{ post.title }}</a></h3>
                            <p>{{ post.content[:200] }}{% if post.content|length > 200 %}...{% endif %}</p>
                        </div>

                        <div class="post-footer">
                            <div class="post-actions">
                                <button class="action-btn like-btn" data-post-id="{{ post._id }}">
                                    <i class="fas fa-heart"></i>
                                    <span>{{ post.likes|length }}</span>
                                </button>
                                <a href="{{ url_for('main.view_post', post_id=post._id) }}" class="action-btn">
                                    <i class="fas fa-comment"></i>
                                    <span>{{ post.comment_count or 0 }}</span>
                                </a>
                            </div>
                            <a href="{{ url_for('main.view_post', post_id=post._id) }}" class="read-more">
                                Read More <i class="fas fa-arrow-right"></i>
                            </a>
                        </div>
                    </article>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if pagination %}
                <div class="pagination">
                    {% if pagination.has_prev %}
                        <a href="{{ url_for('main.forum', page=pagination.prev_num, sort=sort_by) }}" class="page-btn">
                            <i class="fas fa-chevron-left"></i> Previous
                        </a>
                    {% endif %}
                    
                    {% for page_num in pagination.iter_pages() %}
                        {% if page_num %}
                            {% if page_num != pagination.page %}
                                <a href="{{ url_for('main.forum', page=page_num, sort=sort_by) }}" class="page-btn">{{ page_num }}</a>
                            {% else %}
                                <span class="page-btn active">{{ page_num }}</span>
                            {% endif %}
                        {% else %}
                            <span class="page-btn disabled">...</span>
                        {% endif %}
                    {% endfor %}
                    
                    {% if pagination.has_next %}
                        <a href="{{ url_for('main.forum', page=pagination.next_num, sort=sort_by) }}" class="page-btn">
                            Next <i class="fas fa-chevron-right"></i>
                        </a>
                    {% endif %}
                </div>
                {% endif %}
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-comments"></i>
                    <h3>No posts yet</h3>
                    <p>Be the first to share something with the community!</p>
                    {% if session.user_id %}
                    <a href="{{ url_for('main.create_post') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i>
                        Create First Post
                    </a>
                    {% endif %}
                </div>
            {% endif %}
        </div>
    </div>
</div>

<script>
// Like functionality
document.querySelectorAll('.like-btn').forEach(btn => {
    btn.addEventListener('click', async function() {
        const postId = this.dataset.postId;
        try {
            const response = await fetch(`/like_post/${postId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            const data = await response.json();
            if (data.success) {
                const countSpan = this.querySelector('span');
                countSpan.textContent = data.likes_count;
                this.classList.toggle('liked', data.liked);
            }
        } catch (error) {
            console.error('Error:', error);
        }
    });
});
</script>
{% endblock %}
