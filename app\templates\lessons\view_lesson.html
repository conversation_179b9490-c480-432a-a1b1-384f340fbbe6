{% extends "base.html" %}

{% block title %}{{ lesson.name }} Lesson - MeduLearn{% endblock %}

{% block content %}
<div class="lesson-container">
    <div class="lesson-header">
        <div class="lesson-nav">
            <a href="{{ url_for('main.lessons') }}" class="back-btn">
                <i class="fas fa-arrow-left"></i> Back to Lessons
            </a>
            <div class="language-selector">
                <select id="language-select" class="form-control">
                    <option value="english">English</option>
                    <option value="kannada">ಕನ್ನಡ (Kannada)</option>
                    <option value="hindi">हिंदी (Hindi)</option>
                    <option value="telugu">తెలుగు (Telugu)</option>
                </select>
            </div>
        </div>
        
        <div class="lesson-title">
            <h1 id="lesson-name">{{ lesson.name }}</h1>
            <div class="lesson-translations">
                <span class="translation" data-lang="kannada">{{ lesson.name_kannada }}</span>
                <span class="translation" data-lang="hindi">{{ lesson.name_hindi }}</span>
                <span class="translation" data-lang="telugu">{{ lesson.name_telugu }}</span>
            </div>
        </div>
    </div>

    <div class="lesson-content">
        <div class="content-section">
            <h2><i class="fas fa-info-circle"></i> What is <span id="disease-name">{{ lesson.name }}</span>?</h2>
            <div class="content-text" id="explanation-text">
                {{ lesson.content.english }}
            </div>
            <button class="audio-btn" onclick="speakText('explanation-text')">
                <i class="fas fa-volume-up"></i> Listen
            </button>
        </div>

        <div class="content-section">
            <h2><i class="fas fa-exclamation-triangle"></i> Symptoms</h2>
            <div class="symptoms-list" id="symptoms-list">
                {% for symptom in lesson.symptoms.english %}
                <div class="symptom-item">
                    <i class="fas fa-check-circle"></i>
                    <span>{{ symptom }}</span>
                </div>
                {% endfor %}
            </div>
            <button class="audio-btn" onclick="speakSymptoms()">
                <i class="fas fa-volume-up"></i> Listen to Symptoms
            </button>
        </div>

        <div class="content-section">
            <h2><i class="fas fa-shield-alt"></i> Prevention</h2>
            <div class="prevention-list" id="prevention-list">
                {% for prevention in lesson.prevention.english %}
                <div class="prevention-item">
                    <i class="fas fa-heart"></i>
                    <span>{{ prevention }}</span>
                </div>
                {% endfor %}
            </div>
            <button class="audio-btn" onclick="speakPrevention()">
                <i class="fas fa-volume-up"></i> Listen to Prevention Tips
            </button>
        </div>

        <div class="visual-aids">
            <h2><i class="fas fa-images"></i> Visual Learning Aids</h2>
            <div class="visuals-grid">
                {% for visual in lesson.suggested_visuals %}
                <div class="visual-card">
                    <i class="fas fa-image"></i>
                    <p>{{ visual }}</p>
                </div>
                {% endfor %}
            </div>
        </div>

        <div class="lesson-actions">
            <a href="{{ url_for('main.lesson_quiz', disease_id=disease_id) }}" class="btn btn-primary">
                <i class="fas fa-question-circle"></i> Take Quiz
            </a>
            <a href="{{ url_for('main.chatbot') }}" class="btn btn-secondary">
                <i class="fas fa-robot"></i> Ask AI Assistant
            </a>
            <button class="btn btn-success" onclick="markComplete()">
                <i class="fas fa-check"></i> Mark as Complete
            </button>
        </div>
    </div>
</div>

<style>
.lesson-container {
    max-width: 1000px;
    margin: 0 auto;
    padding: 2rem;
}

.lesson-header {
    margin-bottom: 3rem;
}

.lesson-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.back-btn {
    color: #3498db;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.back-btn:hover {
    color: #2980b9;
}

.language-selector select {
    padding: 0.5rem 1rem;
    border: 2px solid #3498db;
    border-radius: 25px;
    background: white;
    color: #3498db;
    font-weight: 600;
}

.lesson-title {
    text-align: center;
}

.lesson-title h1 {
    color: #2c3e50;
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.lesson-translations {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.translation {
    color: #7f8c8d;
    font-size: 1.2rem;
    font-weight: 600;
}

.content-section {
    background: white;
    padding: 2rem;
    margin-bottom: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.content-section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.content-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #34495e;
    margin-bottom: 1.5rem;
}

.symptoms-list,
.prevention-list {
    display: grid;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.symptom-item,
.prevention-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #e74c3c;
}

.prevention-item {
    border-left-color: #27ae60;
}

.symptom-item i,
.prevention-item i {
    color: #e74c3c;
    font-size: 1.2rem;
}

.prevention-item i {
    color: #27ae60;
}

.audio-btn {
    background: #9b59b6;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.audio-btn:hover {
    background: #8e44ad;
    transform: translateY(-2px);
}

.visual-aids {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    margin-bottom: 2rem;
}

.visual-aids h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.visuals-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.visual-card {
    background: white;
    padding: 1.5rem;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.visual-card i {
    font-size: 2rem;
    color: #3498db;
    margin-bottom: 1rem;
}

.lesson-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.lesson-actions .btn {
    padding: 1rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn-success {
    background: #27ae60;
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}
</style>

<script>
// Lesson content data
const lessonData = {{ lesson | tojson }};
let currentLanguage = 'english';

// Language switching
document.getElementById('language-select').addEventListener('change', function() {
    currentLanguage = this.value;
    updateContent();
});

function updateContent() {
    // Update explanation
    const explanationText = document.getElementById('explanation-text');
    explanationText.textContent = lessonData.content[currentLanguage];
    
    // Update symptoms
    const symptomsList = document.getElementById('symptoms-list');
    symptomsList.innerHTML = '';
    lessonData.symptoms[currentLanguage].forEach(symptom => {
        const item = document.createElement('div');
        item.className = 'symptom-item';
        item.innerHTML = `<i class="fas fa-check-circle"></i><span>${symptom}</span>`;
        symptomsList.appendChild(item);
    });
    
    // Update prevention
    const preventionList = document.getElementById('prevention-list');
    preventionList.innerHTML = '';
    lessonData.prevention[currentLanguage].forEach(prevention => {
        const item = document.createElement('div');
        item.className = 'prevention-item';
        item.innerHTML = `<i class="fas fa-heart"></i><span>${prevention}</span>`;
        preventionList.appendChild(item);
    });
}

// Text-to-speech functionality
function speakText(elementId) {
    const text = document.getElementById(elementId).textContent;
    const utterance = new SpeechSynthesisUtterance(text);
    
    // Set language based on current selection
    const langCodes = {
        'english': 'en-US',
        'kannada': 'kn-IN',
        'hindi': 'hi-IN',
        'telugu': 'te-IN'
    };
    
    utterance.lang = langCodes[currentLanguage] || 'en-US';
    speechSynthesis.speak(utterance);
}

function speakSymptoms() {
    const symptoms = lessonData.symptoms[currentLanguage].join('. ');
    const utterance = new SpeechSynthesisUtterance('Symptoms: ' + symptoms);
    speechSynthesis.speak(utterance);
}

function speakPrevention() {
    const prevention = lessonData.prevention[currentLanguage].join('. ');
    const utterance = new SpeechSynthesisUtterance('Prevention tips: ' + prevention);
    speechSynthesis.speak(utterance);
}

function markComplete() {
    // In a real app, this would save progress to database
    alert('Lesson marked as complete! Great job learning about ' + lessonData.name + '!');
}
</script>
{% endblock %}
