/* Modern Dark Theme Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    width: 100%;
    overflow-x: hidden;
}

body {
    font-family: 'Inter', 'Segoe UI', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    font-weight: 400;
    position: relative;
}

/* Medical Light Theme Color Variables */
:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;
    --secondary-color: #059669;
    --secondary-dark: #047857;
    --accent-color: #dc2626;
    --accent-light: #ef4444;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --info: #06b6d4;

    /* Medical light theme backgrounds */
    --bg-primary: #f8fafc;
    --bg-secondary: #f1f5f9;
    --bg-tertiary: #e2e8f0;
    --bg-card: rgba(255, 255, 255, 0.95);
    --bg-glass: rgba(255, 255, 255, 0.8);

    /* Medical text colors */
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --text-inverse: #ffffff;

    /* Medical border colors */
    --border-primary: rgba(148, 163, 184, 0.2);
    --border-secondary: rgba(148, 163, 184, 0.1);
    --border-accent: rgba(37, 99, 235, 0.3);

    /* Medical shadow colors */
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);

    /* Medical glow effects */
    --glow-primary: 0 0 0 3px rgba(37, 99, 235, 0.1);
    --glow-secondary: 0 0 0 3px rgba(5, 150, 105, 0.1);
    --glow-accent: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

/* Medical Light Navigation */
.navbar {
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-primary);
    box-shadow: var(--shadow-md);
    position: sticky;
    top: 0;
    z-index: 1000;
    padding: 0;
    position: relative;
}

.navbar::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, var(--primary-color), var(--secondary-color), var(--primary-color), transparent);
    opacity: 0.8;
}

.nav-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
    position: relative;
}

.nav-brand {
    display: flex;
    align-items: center;
    font-weight: 800;
    font-size: 1.8rem;
}

.nav-brand a {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    color: var(--primary-color);
    transition: all 0.3s ease;
    position: relative;
}

.nav-brand a:hover {
    color: var(--primary-dark);
    transform: scale(1.05);
}

.nav-brand i {
    font-size: 2rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    list-style: none;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    text-decoration: none;
    color: var(--text-secondary);
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    background: var(--bg-secondary);
    color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.nav-link i {
    font-size: 1rem;
}

.nav-cta {
    background: var(--primary-color);
    color: var(--text-inverse) !important;
    font-weight: 600;
    border: 1px solid var(--primary-color);
}

.nav-cta:hover {
    background: var(--primary-dark);
    color: var(--text-inverse) !important;
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

/* Modern Dropdown Styles */
.nav-dropdown {
    position: relative;
}

.dropdown-toggle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.dropdown-toggle .fas.fa-chevron-down {
    font-size: 0.8rem;
    transition: transform 0.3s ease;
}

.nav-dropdown.active .dropdown-toggle .fas.fa-chevron-down {
    transform: rotate(180deg);
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: var(--bg-primary);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
    min-width: 240px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-20px) scale(0.95);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    margin-top: 0.75rem;
    overflow: hidden;
}

.nav-dropdown.active .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.25rem;
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.08);
    position: relative;
}

.dropdown-item:last-child {
    border-bottom: none;
}

.dropdown-item:hover {
    background: rgba(255, 255, 255, 0.08);
    color: var(--primary-color);
    transform: translateX(5px);
}

.dropdown-item.logout {
    color: var(--error);
}

.dropdown-item.logout:hover {
    background: rgba(239, 68, 68, 0.1);
    color: var(--error);
}

.dropdown-divider {
    height: 1px;
    background: var(--border-primary);
    margin: 0.5rem 0;
}

/* Modern Mobile Navigation Toggle */
.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
}

.nav-toggle:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(0, 212, 255, 0.3);
    box-shadow: 0 0 15px rgba(0, 212, 255, 0.2);
}

.nav-toggle .bar {
    width: 24px;
    height: 3px;
    background: var(--primary-color);
    margin: 3px 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 2px;
    box-shadow: 0 0 5px rgba(0, 212, 255, 0.3);
}

.nav-toggle.active .bar:nth-child(1) {
    transform: rotate(-45deg) translate(-6px, 6px);
}

.nav-toggle.active .bar:nth-child(2) {
    opacity: 0;
    transform: scale(0);
}

.nav-toggle.active .bar:nth-child(3) {
    transform: rotate(45deg) translate(-6px, -6px);
}

/* Modern Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    width: 100%;
    position: relative;
}

/* Medical Background Effects */
.main-content::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 20% 80%, rgba(37, 99, 235, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(5, 150, 105, 0.03) 0%, transparent 50%),
        radial-gradient(circle at 40% 40%, rgba(220, 38, 38, 0.02) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

/* Medical Text Styling */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary) !important;
}

p, span, div {
    color: var(--text-primary) !important;
}

strong, b {
    color: var(--text-primary) !important;
    font-weight: 600;
}

/* Override any black or dark colors */
* {
    color: inherit;
}

/* Global dark color overrides */
*[style*="color: black"],
*[style*="color: #000"],
*[style*="color: #111"],
*[style*="color: #222"],
*[style*="color: #333"],
*[style*="color: #444"],
*[style*="color: #555"],
*[style*="color: #666"] {
    color: var(--text-primary) !important;
}

/* CSS class overrides for dark colors */
.color-333,
.color-444,
.color-555,
.color-666 {
    color: var(--text-primary) !important;
}

/* Ensure all text has good contrast and override dark colors */
* {
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Force white text for common dark color classes */
.text-dark,
.text-black,
.text-muted,
.text-secondary {
    color: var(--text-primary) !important;
}

/* Override Bootstrap and other framework dark text classes */
.text-gray-900,
.text-gray-800,
.text-gray-700,
.text-gray-600,
.text-gray-500 {
    color: var(--text-primary) !important;
}

/* Comprehensive dark color override */
*[style*="color: #333"],
*[style*="color: #444"],
*[style*="color: #555"],
*[style*="color: #666"],
*[style*="color: #777"],
*[style*="color: #888"],
*[style*="color: #999"],
*[style*="color:#333"],
*[style*="color:#444"],
*[style*="color:#555"],
*[style*="color:#666"],
*[style*="color:#777"],
*[style*="color:#888"],
*[style*="color:#999"] {
    color: var(--text-primary) !important;
}

/* Override specific dark color properties in CSS */
* {
    --bs-body-color: var(--text-primary) !important;
    --bs-text-muted: var(--text-secondary) !important;
}

/* Medical Container Styles */
.container {
    background: var(--bg-card);
    padding: 2.5rem;
    border-radius: 16px;
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
    max-width: 480px;
    margin: 2rem auto;
    position: relative;
}

.container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 16px 16px 0 0;
}

.container h2 {
    text-align: center;
    margin-bottom: 2rem;
    color: var(--text-primary);
    font-size: 1.8rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Modern Page Headers */
.page-header {
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-primary);
    padding: 3rem 0;
    margin-bottom: 3rem;
    position: relative;
}

.page-header::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.page-header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

.page-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.page-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin: 0;
    font-weight: 400;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
}

.breadcrumb a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
}

.breadcrumb a:hover {
    background: var(--bg-glass);
    text-shadow: var(--glow-primary);
}

.breadcrumb-separator {
    color: var(--text-muted);
}

/* Modern Form Styles */
.form-group {
    margin-bottom: 2rem;
}

.form-label {
    display: block;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    font-size: 0.95rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-control {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 1px solid var(--border-primary);
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: var(--bg-card);
    color: var(--text-primary);
    font-family: inherit;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--glow-primary);
    background: var(--bg-card);
}

.form-control::placeholder {
    color: #94a3b8;
    font-weight: 400;
    opacity: 0.8;
}

.form-control:disabled {
    background: var(--bg-secondary);
    color: var(--text-muted);
    cursor: not-allowed;
    opacity: 0.6;
}

/* Modern Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    font-size: 0.95rem;
    font-weight: 600;
    border: 2px solid transparent;
    border-radius: 16px;
    cursor: pointer;
    text-decoration: none;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    min-height: 52px;
    position: relative;
    overflow: hidden;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--primary-color);
    color: var(--text-inverse);
    border: 1px solid var(--primary-color);
    box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
    background: var(--bg-tertiary);
    border-color: var(--primary-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: linear-gradient(135deg, var(--success), #059669);
    color: var(--text-inverse);
    border-color: var(--success);
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
}

.btn-success:hover {
    transform: translateY(-3px);
    box-shadow: 0 0 25px rgba(16, 185, 129, 0.4), var(--shadow-lg);
}

.btn-danger {
    background: linear-gradient(135deg, var(--error), #dc2626);
    color: var(--text-inverse);
    border-color: var(--error);
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
}

.btn-danger:hover {
    transform: translateY(-3px);
    box-shadow: 0 0 25px rgba(239, 68, 68, 0.4), var(--shadow-lg);
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: var(--text-inverse);
    box-shadow: var(--glow-primary);
}

.btn-sm {
    padding: 0.75rem 1.5rem;
    font-size: 0.8rem;
    min-height: 44px;
}

.btn-lg {
    padding: 1.25rem 2.5rem;
    font-size: 1.1rem;
    min-height: 60px;
}

.btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

.btn:disabled:hover {
    transform: none !important;
}

/* Medical Card Styles */
.card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: 12px;
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all 0.3s ease;
    position: relative;
}

.card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.card:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-4px);
    border-color: var(--primary-color);
}

.card:hover::before {
    opacity: 1;
}

.card-header {
    padding: 2rem;
    border-bottom: 1px solid var(--border-secondary);
    background: var(--bg-glass);
}

.card-title {
    font-size: 1.4rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card-subtitle {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin: 0.75rem 0 0 0;
    font-weight: 400;
}

.card-body {
    padding: 2rem;
    color: var(--text-primary);
}

.card-body p {
    color: var(--text-secondary);
    line-height: 1.7;
}

.card-body strong {
    color: var(--text-primary);
    font-weight: 600;
}

.card-footer {
    padding: 1.5rem 2rem;
    border-top: 1px solid var(--border-secondary);
    background: var(--bg-glass);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* Modern Grid Layouts */
.grid {
    display: grid;
    gap: 2rem;
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

.grid-auto-fit {
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
}

.grid-auto-fill {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
}

/* Modern Section Styles */
.section {
    padding: 4rem 0;
    position: relative;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.section-subtitle {
    font-size: 1.3rem;
    color: var(--text-secondary);
    max-width: 700px;
    margin: 0 auto;
    font-weight: 400;
    line-height: 1.7;
}

.section-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Modern Alert/Flash Messages */
.flash-messages {
    position: fixed;
    top: 100px;
    right: 2rem;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 450px;
}

.flash-message {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.25rem 1.5rem;
    border-radius: 16px;
    border: 1px solid;
    box-shadow: var(--shadow-xl);
    animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    position: relative;
    overflow: hidden;
}

.flash-message::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: currentColor;
}

.flash-success {
    background: rgba(16, 185, 129, 0.1);
    border-color: rgba(16, 185, 129, 0.3);
    color: var(--success);
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.2);
}

.flash-error {
    background: rgba(239, 68, 68, 0.1);
    border-color: rgba(239, 68, 68, 0.3);
    color: var(--error);
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.2);
}

.flash-warning {
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.3);
    color: var(--warning);
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.2);
}

.flash-info {
    background: rgba(6, 182, 212, 0.1);
    border-color: rgba(6, 182, 212, 0.3);
    color: var(--info);
    box-shadow: 0 0 20px rgba(6, 182, 212, 0.2);
}

.flash-close {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    color: inherit;
    opacity: 0.7;
    margin-left: auto;
    transition: all 0.3s ease;
}

.flash-close:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.1);
    transform: scale(1.1);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%) scale(0.8);
        opacity: 0;
    }
    to {
        transform: translateX(0) scale(1);
        opacity: 1;
    }
}

/* Modern Responsive Design */
@media (max-width: 1024px) {
    .grid-cols-3 {
        grid-template-columns: repeat(2, 1fr);
    }

    .grid-cols-4 {
        grid-template-columns: repeat(2, 1fr);
    }

    .section-title {
        font-size: 2.5rem;
    }

    .nav-container {
        padding: 0 1.5rem;
    }

    .section-content {
        padding: 0 1.5rem;
    }
}

@media (max-width: 768px) {
    .nav-toggle {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        top: 80px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 80px);
        background: var(--bg-primary);
        backdrop-filter: blur(20px);
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        flex-direction: column;
        justify-content: flex-start;
        align-items: stretch;
        padding: 2rem;
        gap: 1rem;
        transition: left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 999;
        overflow-y: auto;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-link {
        padding: 1.25rem;
        border-bottom: 1px solid rgba(255, 255, 255, 0.08);
        border-radius: 12px;
        margin-bottom: 0.5rem;
        background: rgba(255, 255, 255, 0.06);
        color: var(--text-primary);
    }

    .nav-link:last-child {
        border-bottom: none;
    }

    .nav-dropdown .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: 1px solid rgba(255, 255, 255, 0.1);
        background: rgba(255, 255, 255, 0.05);
        margin: 0.5rem 0;
        border-radius: 12px;
    }

    .grid-cols-2,
    .grid-cols-3,
    .grid-cols-4 {
        grid-template-columns: 1fr;
    }

    .grid-auto-fit {
        grid-template-columns: 1fr;
    }

    .section {
        padding: 3rem 0;
    }

    .section-title {
        font-size: 2rem;
    }

    .section-subtitle {
        font-size: 1.1rem;
    }

    .page-title {
        font-size: 2rem;
    }

    .page-subtitle {
        font-size: 1.1rem;
    }

    .card-header {
        padding: 1.5rem;
    }

    .card-body {
        padding: 1.5rem;
    }

    .card-footer {
        padding: 1rem 1.5rem;
    }

    .container {
        max-width: 100%;
        margin: 1.5rem;
        padding: 2rem;
    }

    /* Make form grids single column on mobile */
    div[style*="grid-template-columns: 1fr 1fr"] {
        display: block !important;
    }

    div[style*="grid-template-columns: 1fr 1fr"] .form-group {
        margin-bottom: 1.5rem;
    }

    .flash-messages {
        right: 1rem;
        left: 1rem;
        max-width: none;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 1rem;
        height: 70px;
    }

    .section-content {
        padding: 0 1rem;
    }

    .page-header-content {
        padding: 0 1rem;
    }

    .section-title {
        font-size: 1.8rem;
    }

    .page-title {
        font-size: 1.6rem;
    }

    .card-title {
        font-size: 1.2rem;
    }

    .btn {
        padding: 0.75rem 1.5rem;
        font-size: 0.85rem;
    }

    .container {
        margin: 1rem;
        padding: 1.5rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .footer-links {
        gap: 2rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .nav-brand a {
        font-size: 1.5rem;
    }

    .nav-brand i {
        font-size: 1.6rem;
    }
}

/* Medical Footer */
.footer {
    background: var(--bg-card);
    border-top: 1px solid var(--border-primary);
    padding: 1.5rem 0;
    margin-top: auto;
    flex-shrink: 0;
    box-shadow: var(--shadow-sm);
    position: relative;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.footer-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 700;
    color: var(--primary-color);
    font-size: 1.2rem;
}

.footer-logo i {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.4rem;
}

.footer-tagline {
    color: var(--text-secondary);
    font-size: 0.8rem;
    font-weight: 400;
}

.footer-links {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.footer-link {
    color: var(--text-primary);
    text-decoration: none;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
}

.footer-link:hover {
    color: var(--primary-color);
    transform: translateY(-2px);
    text-shadow: var(--glow-primary);
}

.footer-link::after {
    content: '';
    position: absolute;
    bottom: 0.25rem;
    left: 0;
    width: 0;
    height: 1px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    transition: width 0.3s ease;
    border-radius: 1px;
}

.footer-link:hover::after {
    width: 100%;
}

.footer-copyright {
    color: var(--text-secondary);
    font-size: 0.75rem;
    font-weight: 400;
}

/* Footer Responsive Design */
@media (max-width: 768px) {
    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .footer-brand {
        justify-content: center;
    }

    .footer-links {
        gap: 1.5rem;
        flex-wrap: wrap;
        justify-content: center;
    }

    .footer-copyright {
        text-align: center;
        font-size: 0.7rem;
    }
}

@media (max-width: 480px) {
    .footer {
        padding: 1rem 0;
    }

    .footer-content {
        gap: 0.75rem;
        padding: 0 1rem;
    }

    .footer-links {
        gap: 1rem;
        flex-direction: row;
        flex-wrap: wrap;
    }

    .footer-link {
        font-size: 0.75rem;
        padding: 0.25rem 0;
    }

    .footer-copyright {
        font-size: 0.65rem;
    }

    .footer-logo {
        font-size: 1rem;
    }

    .footer-logo i {
        font-size: 1.2rem;
    }

    .footer-tagline {
        font-size: 0.7rem;
    }
}

/* Modern Form Elements */
label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: var(--text-primary);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

input[type="text"],
input[type="password"],
input[type="email"] {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid var(--border-primary);
    border-radius: 16px;
    font-size: 1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    color: var(--text-primary);
    font-family: inherit;
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="email"]:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: var(--glow-primary);
    background: var(--bg-card);
    transform: translateY(-2px);
}

input[type="submit"] {
    width: 100%;
    padding: 1rem 1.5rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--text-inverse);
    border: none;
    border-radius: 16px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

input[type="submit"]::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

input[type="submit"]:hover::before {
    left: 100%;
}

input[type="submit"]:hover {
    transform: translateY(-3px);
    box-shadow: var(--glow-primary), var(--shadow-lg);
}

/* Modern Error Messages */
.error {
    color: var(--error);
    font-size: 0.875rem;
    margin-top: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
}

.error::before {
    content: '⚠';
    font-size: 1rem;
}

/* Modern Dashboard Elements */
.user-data {
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    padding: 2rem;
    border-radius: 20px;
    margin: 1.5rem 0;
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-lg);
}

.user-data h2 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-weight: 700;
}

.user-data ul {
    list-style: none;
}

.user-data li {
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--border-secondary);
    color: var(--text-primary);
    font-weight: 500;
}

.user-data li strong {
    color: var(--primary-color);
}

/* Modern Links */
a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

a:hover {
    color: var(--primary-light);
    text-shadow: var(--glow-primary);
}

/* Medical Dashboard Specific Styles */
.hero-section {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    padding: 4rem 2rem;
    text-align: center;
    color: var(--text-inverse);
    margin: 0;
    min-height: 35vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.hero-content {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
}

.hero-title {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    font-weight: 700;
    color: var(--text-inverse);
}

.hero-title i {
    margin-right: 1rem;
    color: var(--accent-color);
}

.hero-subtitle {
    font-size: 1.3rem;
    opacity: 0.95;
    margin: 0;
    color: var(--text-inverse);
    font-weight: 400;
}

.quick-nav-section {
    background: rgba(255, 255, 255, 0.95);
    padding: 3rem 2rem;
    margin: 0;
    min-height: auto;
    display: flex;
    align-items: center;
}

.quick-nav-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    width: 100%;
    max-width: 1400px;
    margin: 0 auto;
}

.quick-nav-card {
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    padding: 2.5rem;
    border-radius: 20px;
    text-decoration: none;
    color: var(--text-primary);
    transition: all 0.3s ease;
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
    border: 1px solid var(--border-primary);
    min-height: 280px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.quick-nav-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl), var(--glow-primary);
    color: var(--text-primary);
    text-decoration: none;
    border-color: var(--border-accent);
}

.nav-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #e91e63 0%, #f06292 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1.5rem;
    box-shadow: 0 8px 20px rgba(233, 30, 99, 0.3);
}

.nav-icon i {
    font-size: 1.5rem;
    color: white;
}

.quick-nav-card h3 {
    font-size: 1.4rem;
    margin-bottom: 0.5rem;
    color: #e91e63;
    font-weight: 700;
}

.quick-nav-card h4 {
    font-size: 0.9rem;
    margin-bottom: 1rem;
    color: #666;
    font-weight: 500;
    opacity: 0.8;
}

.quick-nav-card p {
    color: #555;
    margin-bottom: 1rem;
    line-height: 1.6;
    font-size: 1rem;
}

.nav-arrow {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    opacity: 0.5;
    transition: all 0.3s ease;
}

.quick-nav-card:hover .nav-arrow {
    opacity: 1;
    transform: translateX(5px);
}

.dashboard-container {
    width: 100%;
    margin: 0;
    padding: 3rem 2rem;
    background: rgba(255, 255, 255, 0.95);
    min-height: auto;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
    max-width: 1400px;
    margin: 2rem auto 0;
}

.dashboard-card {
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    padding: 2.5rem;
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    border: 1px solid var(--border-primary);
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 50px rgba(233, 30, 99, 0.15);
    border-color: rgba(233, 30, 99, 0.2);
}

.dashboard-card h2 {
    color: #e91e63;
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    border-bottom: 2px solid rgba(233, 30, 99, 0.2);
    padding-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.dashboard-card h2 i {
    font-size: 1.2rem;
}

.profile-info p, .preferences-info p {
    margin-bottom: 0.75rem;
    padding: 0.5rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 5px;
}

.profile-completion {
    margin-top: 1.5rem;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 10px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #e91e63 0%, #f06292 100%);
    transition: width 0.3s ease;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
}

.stat-item {
    text-align: center;
    padding: 1.5rem;
    background: rgba(233, 30, 99, 0.05);
    border-radius: 10px;
    transition: all 0.3s ease;
    border: 1px solid rgba(233, 30, 99, 0.1);
}

.stat-item:hover {
    background: rgba(233, 30, 99, 0.1);
    transform: translateY(-2px);
}

.stat-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #e91e63 0%, #f06292 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.stat-icon i {
    color: white;
    font-size: 1rem;
}

.stat-item h3 {
    color: #e91e63;
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.stat-item p {
    font-weight: 600;
    color: #333;
}

.stat-number {
    font-size: 1.5rem !important;
    color: #e91e63 !important;
}

/* Activity Section */
.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(233, 30, 99, 0.05);
    border-radius: 10px;
    border-left: 4px solid #e91e63;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #e91e63 0%, #f06292 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
    flex-shrink: 0;
}

.activity-content p {
    margin: 0 0 0.25rem 0;
    color: #333;
}

.activity-time {
    font-size: 0.85rem;
    color: #666;
}

/* Preferences Section */
.preference-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem;
    background: rgba(233, 30, 99, 0.05);
    border-radius: 8px;
    margin-bottom: 0.75rem;
}

.preference-item i {
    color: #e91e63;
    width: 20px;
    text-align: center;
}

.quick-actions {
    display: grid;
    gap: 1rem;
}

.action-btn {
    display: block;
    padding: 0.75rem;
    background: linear-gradient(135deg, #e91e63 0%, #f06292 100%);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    text-align: center;
    transition: transform 0.3s ease;
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(233, 30, 99, 0.4);
}

.logout-btn {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
}

.logout-btn:hover {
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
}

/* Profile form styles */
.form-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 10px;
}

.form-section h3 {
    color: #667eea;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.checkbox-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
}

.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-right: 1rem;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-secondary {
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    color: var(--text-primary);
    border: 1px solid var(--border-primary);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

textarea.form-control {
    resize: vertical;
    min-height: 100px;
}

select.form-control {
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    cursor: pointer;
    color: var(--text-primary);
}

/* Authentication Pages Styles */
.auth-body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    overflow-x: hidden;
}

.auth-container {
    position: relative;
    width: 100%;
    max-width: 500px;
    margin: 2rem;
    z-index: 10;
}

.register-card {
    max-width: 600px !important;
}

.auth-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
}

.auth-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
    background-size: 200% 100%;
    animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
    0%, 100% { background-position: 200% 0; }
    50% { background-position: -200% 0; }
}

.auth-header {
    text-align: center;
    margin-bottom: 2rem;
}

.auth-logo {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.auth-logo i {
    font-size: 2rem;
    color: white;
}

.auth-header h1 {
    color: #333;
    font-size: 2rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
}

.auth-header p {
    color: #666;
    font-size: 1rem;
    margin: 0;
}

.flash-messages {
    margin-bottom: 1.5rem;
}

.flash-message {
    padding: 1rem;
    border-radius: 10px;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 500;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.flash-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-left: 4px solid #28a745;
}

.flash-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.auth-form {
    margin-bottom: 2rem;
    animation: formSlideIn 0.8s ease-out;
}

@keyframes formSlideIn {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    animation: groupSlideIn 0.6s ease-out both;
}

.form-group {
    margin-bottom: 1.5rem;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    gap: 1rem;
}

.input-icon {
    color: #999;
    font-size: 1.2rem;
    min-width: 20px;
    text-align: center;
    transition: color 0.3s ease;
    opacity: 0.7;
}

.form-input {
    flex: 1;
    padding: 1.2rem 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    font-size: 1rem;
    background: rgba(255, 255, 255, 0.9);
    transition: border-color 0.3s ease, box-shadow 0.3s ease, background-color 0.3s ease;
    outline: none;
    line-height: 1.2;
    min-height: 56px;
    position: relative;
}

.form-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15);
    background: white;
}

.form-input:not(:placeholder-shown),
.form-input:focus,
.form-input.has-value {
    padding-top: 1.8rem;
    padding-bottom: 0.6rem;
}

/* Icon color changes on focus */
.input-group:focus-within .input-icon {
    color: #667eea;
    opacity: 1;
}

/* Enhanced input group styling */
.input-group:hover .input-icon {
    color: #667eea;
}

/* Smooth container animation */
.form-group {
    margin-bottom: 1.8rem;
    animation: groupSlideIn 0.6s ease-out both;
}

@keyframes groupSlideIn {
    0% {
        opacity: 0;
        transform: translateY(20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Floating Label Styles */
.floating-label {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #999;
    font-size: 1rem;
    pointer-events: none;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 4;
    white-space: nowrap;
    font-weight: 500;
    opacity: 0.8;
    background: transparent;
}

/* When input is focused or has content */
.form-input:focus + .floating-label,
.form-input:not(:placeholder-shown) + .floating-label {
    transform: translateY(-2.8rem) scale(0.8);
    color: #667eea;
    background: rgba(255, 255, 255, 0.95);
    padding: 0.2rem 0.6rem;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
    font-weight: 600;
    opacity: 1;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.form-input:focus + .floating-label {
    color: #764ba2;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.25);
    border-color: rgba(118, 75, 162, 0.3);
}

/* Active state classes for JavaScript control */
.floating-label.active {
    transform: translateY(-2.8rem) scale(0.8);
    color: #667eea;
    background: rgba(255, 255, 255, 0.95);
    padding: 0.2rem 0.6rem;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.15);
    font-weight: 600;
    opacity: 1;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.input-icon.active {
    color: #667eea;
    opacity: 1;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    font-size: 1.1rem;
    padding: 0.6rem;
    border-radius: 50%;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 5;
    display: flex;
    align-items: center;
    justify-content: center;
}

.password-toggle:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
    transform: translateY(-50%) scale(1.1);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.password-toggle:active {
    transform: translateY(-50%) scale(0.95);
}

.password-strength {
    margin-bottom: 1rem;
}

.strength-bar {
    width: 100%;
    height: 6px;
    background: #e1e5e9;
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.strength-fill {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 3px;
}

.strength-text {
    font-size: 0.875rem;
    color: #666;
    font-weight: 500;
}

.error-message {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #dc3545;
    font-size: 0.875rem;
    margin-top: 0.5rem;
    font-weight: 500;
}

.auth-btn {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.auth-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.auth-btn:hover::before {
    left: 100%;
}

.auth-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
}

.auth-footer {
    text-align: center;
    padding-top: 1.5rem;
    border-top: 1px solid #e1e5e9;
}

.auth-footer p {
    color: #666;
    margin: 0;
}

.auth-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
}

.auth-link:hover {
    color: #764ba2;
    transform: translateX(3px);
}

/* Floating Background Shapes */
.auth-background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.floating-shapes {
    position: relative;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape-1 {
    width: 100px;
    height: 100px;
    top: 20%;
    left: 10%;
    animation-duration: 6s;
}

.shape-2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 10%;
    animation-duration: 8s;
}

.shape-3 {
    width: 80px;
    height: 80px;
    bottom: 20%;
    left: 20%;
    animation-duration: 7s;
}

.shape-4 {
    width: 120px;
    height: 120px;
    top: 10%;
    right: 30%;
    animation-duration: 9s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}



.nav-dropdown {
    position: relative;
}

.dropdown-toggle {
    cursor: pointer;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    background: white;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(102, 126, 234, 0.1);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
    z-index: 1001;
}

.nav-dropdown.active .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    text-decoration: none;
    color: #555;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 0.25rem;
}

.dropdown-item:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.dropdown-item.logout {
    color: #dc3545;
}

.dropdown-item.logout:hover {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.dropdown-divider {
    height: 1px;
    background: rgba(102, 126, 234, 0.1);
    margin: 0.5rem 0;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.bar {
    width: 25px;
    height: 3px;
    background: #667eea;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.nav-toggle.active .bar:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.nav-toggle.active .bar:nth-child(2) {
    opacity: 0;
}

.nav-toggle.active .bar:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}



/* Enhanced Flash Messages */
.flash-messages {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 1002;
    max-width: 400px;
}

.flash-message {
    background: white;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border-left: 4px solid;
    display: flex;
    align-items: center;
    gap: 1rem;
    animation: slideInRight 0.3s ease-out;
    position: relative;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.flash-success {
    border-left-color: #28a745;
    color: #155724;
}

.flash-danger {
    border-left-color: #dc3545;
    color: #721c24;
}

.flash-close {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    transition: all 0.3s ease;
    margin-left: auto;
}

.flash-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #666;
}



/* Forum Styles */
.forum-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.forum-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.forum-title h1 {
    color: #667eea;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.forum-title p {
    color: #666;
    margin: 0;
}

.forum-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

.stat-card i {
    font-size: 2rem;
    color: #667eea;
}

.stat-card h3 {
    font-size: 1.5rem;
    color: #333;
    margin: 0;
}

.stat-card p {
    color: #666;
    margin: 0;
    font-size: 0.9rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    color: #333;
    margin: 0;
}

.sort-options {
    display: flex;
    gap: 0.5rem;
}

.sort-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    text-decoration: none;
    color: #666;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid #e1e5e9;
}

.sort-btn:hover,
.sort-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.posts-grid {
    display: grid;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.post-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.post-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.post-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
}

.author-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.author-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.author-avatar i {
    font-size: 2rem;
    color: #ccc;
}

.author-details h4 {
    margin: 0;
    color: #333;
    font-weight: 600;
}

.author-details time {
    color: #666;
    font-size: 0.9rem;
}

.post-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
    color: #666;
    font-size: 0.9rem;
}

.views {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.post-image {
    width: 100%;
    max-height: 300px;
    overflow: hidden;
}

.post-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.post-card:hover .post-image img {
    transform: scale(1.05);
}

.post-content {
    padding: 1.5rem;
}

.post-content h3 {
    margin: 0 0 1rem 0;
    color: #333;
}

.post-content h3 a {
    text-decoration: none;
    color: inherit;
    transition: color 0.3s ease;
}

.post-content h3 a:hover {
    color: #667eea;
}

.post-content p {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

.post-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(102, 126, 234, 0.1);
    background: rgba(102, 126, 234, 0.02);
}

.post-actions {
    display: flex;
    gap: 1rem;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: none;
    border: 1px solid #e1e5e9;
    border-radius: 8px;
    color: #666;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.action-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: #667eea;
    color: #667eea;
}

.like-btn.liked {
    background: rgba(220, 53, 69, 0.1);
    border-color: #dc3545;
    color: #dc3545;
}

.read-more {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #667eea;
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
}

.read-more:hover {
    color: #764ba2;
    transform: translateX(3px);
}

.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.empty-state i {
    font-size: 4rem;
    color: #ccc;
    margin-bottom: 1rem;
}

.empty-state h3 {
    color: #333;
    margin-bottom: 0.5rem;
}

.empty-state p {
    color: #666;
    margin-bottom: 2rem;
}

/* Create Post Styles */
.create-post-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 1rem;
}

.create-post-header {
    text-align: center;
    margin-bottom: 2rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.create-post-header h1 {
    color: #667eea;
    margin-bottom: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.create-post-form {
    background: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.form-control {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
    font-family: inherit;
}

.form-control:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.content-textarea {
    min-height: 200px;
    resize: vertical;
}

.character-count {
    text-align: right;
    margin-top: 0.5rem;
    font-size: 0.9rem;
    color: #666;
}

.file-upload-area {
    border: 2px dashed #e1e5e9;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    cursor: pointer;
}

.file-upload-area:hover,
.file-upload-area.drag-over {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.file-input {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    cursor: pointer;
}

.upload-placeholder i {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 1rem;
}

.upload-placeholder p {
    color: #666;
    margin: 0.5rem 0;
    font-weight: 500;
}

.upload-placeholder small {
    color: #999;
}

.image-preview {
    position: relative;
    max-width: 300px;
    margin: 0 auto;
}

.image-preview img {
    width: 100%;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.remove-image {
    position: absolute;
    top: -10px;
    right: -10px;
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.remove-image:hover {
    background: #c82333;
    transform: scale(1.1);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

.post-tips {
    background: rgba(255, 255, 255, 0.95);
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.post-tips h3 {
    color: #667eea;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.post-tips ul {
    list-style: none;
    padding: 0;
}

.post-tips li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0;
    color: #666;
}

.post-tips li i {
    color: #28a745;
}

/* Post View Styles */
.post-view-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 0 1rem;
}

.post-navigation {
    margin-bottom: 2rem;
}

.back-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.95);
    color: #667eea;
    text-decoration: none;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.back-btn:hover {
    background: #667eea;
    color: white;
    transform: translateX(-3px);
}

.post-detail {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.post-detail .post-header {
    padding: 2rem;
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
}

.author-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-section .author-avatar {
    width: 60px;
    height: 60px;
}

.author-info h3 {
    margin: 0;
    color: #333;
}

.author-info time {
    color: #666;
    font-size: 0.9rem;
}

.edited-note {
    display: block;
    color: #999;
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

.post-actions {
    display: flex;
    gap: 0.5rem;
}

.edit-btn {
    background: rgba(40, 167, 69, 0.1);
    border-color: #28a745;
    color: #28a745;
}

.edit-btn:hover {
    background: #28a745;
    color: white;
}

.delete-btn {
    background: rgba(220, 53, 69, 0.1);
    border-color: #dc3545;
    color: #dc3545;
}

.delete-btn:hover {
    background: #dc3545;
    color: white;
}

.post-title {
    font-size: 2rem;
    color: #333;
    margin-bottom: 1.5rem;
    line-height: 1.3;
}

.post-detail .post-content {
    padding: 2rem;
}

.post-detail .post-image {
    margin-bottom: 2rem;
    max-height: 500px;
}

.post-text {
    font-size: 1.1rem;
    line-height: 1.7;
    color: #444;
}

.post-detail .post-footer {
    padding: 1.5rem 2rem;
    background: rgba(102, 126, 234, 0.02);
    border-top: 1px solid rgba(102, 126, 234, 0.1);
}

.post-stats {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666;
    background: none;
    border: none;
    cursor: pointer;
    transition: color 0.3s ease;
}

.stat-item:hover {
    color: #667eea;
}

.stat-item.liked {
    color: #dc3545;
}

/* Comments Section */
.comments-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    padding: 2rem;
}

.comments-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
}

.comments-header h2 {
    color: #333;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.comment-form-container {
    margin-bottom: 2rem;
}

.comment-input-group {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

.commenter-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.commenter-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.commenter-avatar i {
    font-size: 1.5rem;
    color: #ccc;
}

.comment-input-wrapper {
    flex: 1;
}

.comment-input {
    width: 100%;
    min-height: 80px;
    padding: 1rem;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    resize: vertical;
    font-family: inherit;
    transition: border-color 0.3s ease;
}

.comment-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.comment-actions {
    margin-top: 1rem;
    display: flex;
    justify-content: flex-end;
}

.login-prompt {
    text-align: center;
    padding: 2rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 8px;
    margin-bottom: 2rem;
}

.login-prompt a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}



.comment {
    display: flex;
    gap: 1rem;
    padding: 1.5rem 0;
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
}

.comment:last-child {
    border-bottom: none;
}

.comment-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.comment-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.comment-avatar i {
    font-size: 1.5rem;
    color: #ccc;
}

.comment-content {
    flex: 1;
}

.comment-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 0.5rem;
}

.comment-author {
    margin: 0;
    color: #333;
    font-weight: 600;
    font-size: 0.95rem;
}

.comment-time {
    color: #666;
    font-size: 0.85rem;
}

.comment-actions {
    display: flex;
    gap: 0.5rem;
    margin-left: auto;
}

.comment-action-btn {
    background: none;
    border: none;
    color: #999;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.comment-action-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.comment-action-btn.delete:hover {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.comment-text {
    color: #444;
    line-height: 1.6;
    margin-bottom: 0.5rem;
}

.comment-edited {
    color: #999;
    font-size: 0.8rem;
}

.no-comments {
    text-align: center;
    padding: 3rem;
    color: #666;
}

.no-comments i {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 1rem;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    margin-top: 2rem;
}

.page-btn {
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.95);
    color: #667eea;
    text-decoration: none;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
    transition: all 0.3s ease;
    font-weight: 500;
}

.page-btn:hover {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.page-btn.active {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.page-btn.disabled {
    color: #ccc;
    cursor: not-allowed;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        flex-direction: column;
        justify-content: flex-start;
        align-items: stretch;
        padding: 2rem;
        gap: 1rem;
        transition: left 0.3s ease;
        z-index: 999;
    }

    .nav-menu.active {
        left: 0;
    }

    .nav-toggle {
        display: flex;
    }

    .nav-link {
        justify-content: center;
        padding: 1rem;
        border: 1px solid #e1e5e9;
        border-radius: 8px;
    }

    .nav-dropdown .dropdown-menu {
        position: static;
        opacity: 1;
        visibility: visible;
        transform: none;
        box-shadow: none;
        border: none;
        background: rgba(102, 126, 234, 0.05);
        margin-top: 0.5rem;
    }

    .forum-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .forum-stats {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .sort-options {
        justify-content: center;
    }

    .post-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .post-footer {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .post-actions {
        justify-content: center;
    }

    .author-section {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .post-stats {
        justify-content: center;
        flex-wrap: wrap;
    }

    .comment-input-group {
        flex-direction: column;
        gap: 0.5rem;
    }

    .commenter-avatar {
        align-self: center;
    }

    .comment {
        flex-direction: column;
        gap: 0.5rem;
    }

    .comment-avatar {
        align-self: flex-start;
    }

    .comment-header {
        flex-wrap: wrap;
    }

    .form-actions {
        flex-direction: column;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-links {
        justify-content: center;
    }

    .flash-messages {
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .pagination {
        flex-wrap: wrap;
    }
}

/* Blog Submission Styles */
.blog-submission-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 0 1rem;
}

.blog-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    padding: 3rem 2rem;
    border-radius: 20px;
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.blog-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23667eea" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="%23764ba2" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="%23667eea" opacity="0.15"/><circle cx="10" cy="60" r="0.5" fill="%23764ba2" opacity="0.15"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    pointer-events: none;
}

.header-content {
    flex: 1;
    z-index: 2;
    position: relative;
}

.header-content h1 {
    color: #667eea;
    font-size: 2.5rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.subtitle {
    color: #666;
    font-size: 1.2rem;
    line-height: 1.6;
    margin: 0;
}

.header-illustration {
    font-size: 4rem;
    color: rgba(102, 126, 234, 0.3);
    z-index: 2;
    position: relative;
}

.blog-form-container {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 2rem;
}

.blog-form .form-group {
    margin-bottom: 2rem;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.75rem;
    font-size: 1.1rem;
}

.required {
    color: #dc3545;
    font-weight: bold;
}

.textarea-container {
    position: relative;
}

.story-textarea {
    min-height: 200px;
    resize: vertical;
    font-family: 'Georgia', 'Times New Roman', serif;
    font-size: 1.1rem;
    line-height: 1.7;
    padding: 1.5rem;
}

.character-count {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.75rem;
    font-size: 0.9rem;
    color: #666;
}

.word-count {
    color: #999;
}

.tag-suggestions {
    margin-top: 1rem;
}

.suggestion-label {
    display: block;
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.tag-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag-btn {
    background: rgba(102, 126, 234, 0.1);
    border: 1px solid rgba(102, 126, 234, 0.3);
    color: #667eea;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tag-btn:hover {
    background: #667eea;
    color: white;
    transform: translateY(-1px);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid rgba(102, 126, 234, 0.1);
}

.btn.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: #ccc !important;
}

.review-notice {
    display: flex;
    gap: 1rem;
    background: linear-gradient(135deg, rgba(40, 167, 69, 0.1) 0%, rgba(25, 135, 84, 0.1) 100%);
    padding: 1.5rem;
    border-radius: 15px;
    border-left: 4px solid #28a745;
    margin-top: 2rem;
}

.notice-icon {
    font-size: 1.5rem;
    color: #28a745;
    flex-shrink: 0;
}

.notice-content h4 {
    color: #28a745;
    margin: 0 0 0.5rem 0;
    font-size: 1.1rem;
}

.notice-content p {
    color: #155724;
    margin: 0;
    line-height: 1.6;
}

.writing-tips {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-top: 2rem;
}

.writing-tips h3 {
    color: #667eea;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.3rem;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.tip {
    text-align: center;
    padding: 1.5rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 15px;
    transition: transform 0.3s ease;
}

.tip:hover {
    transform: translateY(-3px);
}

.tip i {
    font-size: 2rem;
    color: #667eea;
    margin-bottom: 1rem;
}

.tip h4 {
    color: #333;
    margin-bottom: 0.5rem;
    font-size: 1.1rem;
}

.tip p {
    color: #666;
    margin: 0;
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Enhanced file upload for blog */
.blog-form .file-upload-area {
    border: 2px dashed rgba(102, 126, 234, 0.3);
    background: rgba(102, 126, 234, 0.02);
    transition: all 0.3s ease;
}

.blog-form .file-upload-area:hover,
.blog-form .file-upload-area.drag-over {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.08);
    transform: translateY(-2px);
}

.blog-form .upload-placeholder i {
    color: #667eea;
    margin-bottom: 1rem;
}

.blog-form .upload-placeholder p {
    color: #667eea;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

/* Responsive design for blog submission */
@media (max-width: 768px) {
    .blog-header {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .header-content h1 {
        font-size: 2rem;
        justify-content: center;
    }

    .header-illustration {
        font-size: 3rem;
    }

    .blog-form-container {
        padding: 2rem 1.5rem;
    }

    .form-actions {
        flex-direction: column;
    }

    .tips-grid {
        grid-template-columns: 1fr;
    }

    .tag-buttons {
        justify-content: center;
    }

    .review-notice {
        flex-direction: column;
        text-align: center;
    }
}

/* ========================================
   FINAL DARK COLOR OVERRIDES
   ======================================== */

/* Force all dark colors to white - highest priority */
*[style*="color: #333"],
*[style*="color: #444"],
*[style*="color: #555"],
*[style*="color: #666"],
*[style*="color: #777"],
*[style*="color: #888"],
*[style*="color: #999"],
*[style*="color:#333"],
*[style*="color:#444"],
*[style*="color:#555"],
*[style*="color:#666"],
*[style*="color:#777"],
*[style*="color:#888"],
*[style*="color:#999"] {
    color: var(--text-primary) !important;
}

/* Override specific CSS properties with dark colors */
.quick-nav-card,
.stat-item p,
.activity-content p,
.auth-header h1,
.auth-header p,
.form-section h3,
.stat-card h3,
.stat-card p,
.section-header h2,
.author-details h4,
.author-details time,
.post-content h3,
.post-content p,
.empty-state h3,
.empty-state p,
.create-post-header h1,
.post-title,
.post-body,
.comments-header h2,
.comment-author,
.comment-time,
.comment-text,
.header-content h1,
.subtitle,
.tip h4,
.tip p {
    color: var(--text-primary) !important;
}

/* Secondary text elements */
.activity-time,
.strength-text,
.auth-footer p,
.forum-title p,
.upload-placeholder p {
    color: var(--text-secondary) !important;
}

/* ========================================
   WHITE BACKGROUND OVERRIDES
   ======================================== */

/* Fix all white/light background elements */
*[style*="background: white"],
*[style*="background: #fff"],
*[style*="background: rgba(255, 255, 255"],
.auth-card,
.flash-message,
.post-card,
.stat-card,
.comments-section,
.post-detail,
.blog-form-container,
.writing-tips,
.create-post-form,
.post-tips,
.empty-state,
.quick-nav-section,
.dashboard-container {
    background: var(--bg-card) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid var(--border-primary) !important;
    box-shadow: var(--shadow-lg) !important;
}

/* Fix form inputs with white backgrounds */
.form-input:focus,
.form-control:focus {
    background: var(--bg-glass) !important;
    color: var(--text-primary) !important;
}

/* Fix floating labels */
.floating-label.active,
.form-input:not(:placeholder-shown) + .floating-label {
    background: var(--bg-card) !important;
    color: var(--primary-color) !important;
}

/* Fix page buttons */
.page-btn {
    background: var(--bg-glass) !important;
    backdrop-filter: blur(10px) !important;
    color: var(--text-primary) !important;
    border: 1px solid var(--border-primary) !important;
}

.page-btn:hover {
    background: var(--bg-card) !important;
    color: var(--primary-color) !important;
    border-color: var(--border-accent) !important;
}
