from flask import Flask
from flask_pymongo import PyMongo
from config import Config

mongo = PyMongo()

def init_db():
    """Initialize database collections and indexes"""
    try:
        # Force database creation by inserting and removing a dummy document
        dummy_doc = {"_temp": "temp"}
        result = mongo.db.temp_collection.insert_one(dummy_doc)
        mongo.db.temp_collection.delete_one({"_id": result.inserted_id})
        # Clean up the temporary collection
        mongo.db.drop_collection("temp_collection")

        # Create users collection with unique index on username
        mongo.db.users.create_index("username", unique=True)

        # Create indexes for posts collection
        mongo.db.posts.create_index([("created_at", -1)])  # For sorting by date
        mongo.db.posts.create_index([("author_id", 1)])    # For user's posts
        mongo.db.posts.create_index([("views", -1)])       # For sorting by popularity

        # Create indexes for comments collection
        mongo.db.comments.create_index([("post_id", 1)])   # For post comments
        mongo.db.comments.create_index([("author_id", 1)]) # For user's comments
        mongo.db.comments.create_index([("created_at", 1)]) # For sorting comments

        # Create indexes for blogs collection
        mongo.db.blogs.create_index([("created_at", -1)])  # For sorting by date
        mongo.db.blogs.create_index([("author_id", 1)])    # For user's blogs
        mongo.db.blogs.create_index([("status", 1)])       # For filtering by status
        mongo.db.blogs.create_index([("tags", 1)])         # For tag-based searches

        # Create a test user if no users exist (for database visibility)
        if mongo.db.users.count_documents({}) == 0:
            from werkzeug.security import generate_password_hash
            test_user = {
                "username": "testuser",
                "password": generate_password_hash("testpass123", method='sha256')
            }
            mongo.db.users.insert_one(test_user)
            print("Created test user: testuser / testpass123")

        # Verify database exists
        client = mongo.cx
        db_list = client.list_database_names()

        print("Database initialized successfully!")
        print(f"Connected to database: {mongo.db.name}")
        print(f"Database exists in MongoDB: {'student_app' in db_list}")
        print("All databases:", db_list)
        print("Collections available:", mongo.db.list_collection_names())
        print(f"MongoDB URI: {mongo.cx.address}")
    except Exception as e:
        print(f"Database initialization error: {e}")
        import traceback
        traceback.print_exc()

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)

    mongo.init_app(app)

    # Initialize database when app starts
    with app.app_context():
        init_db()

    from app import routes
    app.register_blueprint(routes.bp)

    # Add custom template filters
    @app.template_filter('nl2br')
    def nl2br_filter(text):
        """Convert newlines to HTML line breaks"""
        if text:
            return text.replace('\n', '<br>')
        return text

    return app