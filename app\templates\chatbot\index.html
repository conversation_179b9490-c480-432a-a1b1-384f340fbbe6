{% extends "base.html" %}

{% block title %}AI Medical Assistant - MeduLearn{% endblock %}

{% block content %}
<div class="chatbot-container">
    <div class="chatbot-header">
        <h1><i class="fas fa-robot"></i> AI Medical Assistant</h1>
        <p>Ask medical questions in your preferred language</p>
        <div class="language-selector">
            <select id="chat-language" class="form-control">
                <option value="english">English</option>
                <option value="kannada">ಕನ್ನಡ (Kannada)</option>
                <option value="hindi">हिंदी (Hindi)</option>
                <option value="telugu">తెలుగు (Telugu)</option>
            </select>
        </div>
    </div>

    <div class="chat-container">
        <div class="chat-messages" id="chat-messages">
            <div class="message bot-message">
                <div class="message-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="message-content">
                    <p>Hello! I'm your AI medical assistant. I can help answer basic medical questions in multiple languages. How can I help you today?</p>
                    <small class="message-time">Just now</small>
                </div>
            </div>
        </div>

        <div class="quick-questions">
            <h3>Quick Questions:</h3>
            <div class="quick-buttons">
                <button class="quick-btn" onclick="askQuickQuestion('What is diabetes?')">
                    What is diabetes?
                </button>
                <button class="quick-btn" onclick="askQuickQuestion('Fever symptoms')">
                    Fever symptoms
                </button>
                <button class="quick-btn" onclick="askQuickQuestion('High blood pressure')">
                    High blood pressure
                </button>
                <button class="quick-btn" onclick="askQuickQuestion('Heart attack signs')">
                    Heart attack signs
                </button>
            </div>
        </div>

        <div class="chat-input-container">
            <div class="chat-input">
                <input type="text" id="user-input" placeholder="Type your medical question here..." 
                       onkeypress="handleKeyPress(event)">
                <button onclick="sendMessage()" class="send-btn">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <div class="disclaimer">
        <div class="disclaimer-content">
            <i class="fas fa-exclamation-triangle"></i>
            <div>
                <strong>Medical Disclaimer:</strong>
                <p>This AI assistant provides general health information only. Always consult with qualified healthcare professionals for proper medical advice, diagnosis, and treatment.</p>
            </div>
        </div>
    </div>
</div>

<style>
.chatbot-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 2rem;
}

.chatbot-header {
    text-align: center;
    margin-bottom: 2rem;
}

.chatbot-header h1 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 2.5rem;
}

.chatbot-header p {
    color: #7f8c8d;
    font-size: 1.2rem;
    margin-bottom: 1.5rem;
}

.language-selector select {
    padding: 0.5rem 1rem;
    border: 2px solid #3498db;
    border-radius: 25px;
    background: white;
    color: #3498db;
    font-weight: 600;
    min-width: 200px;
}

.chat-container {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.chat-messages {
    height: 400px;
    overflow-y: auto;
    padding: 1.5rem;
    background: #f8f9fa;
}

.message {
    display: flex;
    margin-bottom: 1.5rem;
    animation: fadeIn 0.3s ease;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    flex-shrink: 0;
}

.bot-message .message-avatar {
    background: #3498db;
    color: white;
}

.user-message .message-avatar {
    background: #27ae60;
    color: white;
    order: 2;
    margin-right: 0;
    margin-left: 1rem;
}

.user-message {
    flex-direction: row-reverse;
}

.message-content {
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    max-width: 70%;
}

.user-message .message-content {
    background: #27ae60;
    color: white;
}

.message-content p {
    margin: 0 0 0.5rem 0;
    line-height: 1.6;
}

.message-time {
    color: #7f8c8d;
    font-size: 0.8rem;
}

.user-message .message-time {
    color: rgba(255, 255, 255, 0.8);
}

.quick-questions {
    padding: 1.5rem;
    border-top: 1px solid #ecf0f1;
    background: white;
}

.quick-questions h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.quick-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.quick-btn {
    background: #ecf0f1;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    color: #2c3e50;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.quick-btn:hover {
    background: #3498db;
    color: white;
    transform: translateY(-2px);
}

.chat-input-container {
    padding: 1.5rem;
    background: white;
    border-top: 1px solid #ecf0f1;
}

.chat-input {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.chat-input input {
    flex: 1;
    padding: 1rem 1.5rem;
    border: 2px solid #ecf0f1;
    border-radius: 25px;
    font-size: 1rem;
    outline: none;
    transition: border-color 0.3s ease;
}

.chat-input input:focus {
    border-color: #3498db;
}

.send-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #3498db;
    color: white;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-btn:hover {
    background: #2980b9;
    transform: scale(1.1);
}

.disclaimer {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 10px;
    padding: 1.5rem;
}

.disclaimer-content {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.disclaimer-content i {
    color: #f39c12;
    font-size: 1.5rem;
    margin-top: 0.25rem;
}

.disclaimer-content strong {
    color: #856404;
    display: block;
    margin-bottom: 0.5rem;
}

.disclaimer-content p {
    color: #856404;
    margin: 0;
    line-height: 1.6;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #7f8c8d;
    font-style: italic;
}

.typing-dots {
    display: flex;
    gap: 0.25rem;
}

.typing-dots span {
    width: 6px;
    height: 6px;
    background: #7f8c8d;
    border-radius: 50%;
    animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) { animation-delay: 0.2s; }
.typing-dots span:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing {
    0%, 60%, 100% { transform: translateY(0); }
    30% { transform: translateY(-10px); }
}
</style>

<script>
let currentLanguage = 'english';

document.getElementById('chat-language').addEventListener('change', function() {
    currentLanguage = this.value;
});

function handleKeyPress(event) {
    if (event.key === 'Enter') {
        sendMessage();
    }
}

function askQuickQuestion(question) {
    document.getElementById('user-input').value = question;
    sendMessage();
}

function sendMessage() {
    const input = document.getElementById('user-input');
    const message = input.value.trim();
    
    if (!message) return;
    
    // Add user message
    addMessage(message, 'user');
    input.value = '';
    
    // Show typing indicator
    showTypingIndicator();
    
    // Send to backend
    fetch('/chatbot/ask', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            question: message,
            language: currentLanguage
        })
    })
    .then(response => response.json())
    .then(data => {
        hideTypingIndicator();
        addMessage(data.response, 'bot');
    })
    .catch(error => {
        hideTypingIndicator();
        addMessage('Sorry, I encountered an error. Please try again.', 'bot');
        console.error('Error:', error);
    });
}

function addMessage(text, sender) {
    const messagesContainer = document.getElementById('chat-messages');
    const messageDiv = document.createElement('div');
    messageDiv.className = `message ${sender}-message`;
    
    const avatar = sender === 'bot' ? '<i class="fas fa-robot"></i>' : '<i class="fas fa-user"></i>';
    const time = new Date().toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    
    messageDiv.innerHTML = `
        <div class="message-avatar">${avatar}</div>
        <div class="message-content">
            <p>${text}</p>
            <small class="message-time">${time}</small>
        </div>
    `;
    
    messagesContainer.appendChild(messageDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function showTypingIndicator() {
    const messagesContainer = document.getElementById('chat-messages');
    const typingDiv = document.createElement('div');
    typingDiv.className = 'message bot-message typing-indicator';
    typingDiv.id = 'typing-indicator';
    
    typingDiv.innerHTML = `
        <div class="message-avatar"><i class="fas fa-robot"></i></div>
        <div class="message-content">
            <div class="typing-indicator">
                AI is typing
                <div class="typing-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </div>
        </div>
    `;
    
    messagesContainer.appendChild(typingDiv);
    messagesContainer.scrollTop = messagesContainer.scrollHeight;
}

function hideTypingIndicator() {
    const typingIndicator = document.getElementById('typing-indicator');
    if (typingIndicator) {
        typingIndicator.remove();
    }
}
</script>
{% endblock %}
