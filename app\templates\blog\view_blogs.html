{% extends "base.html" %}

{% block title %}Community Stories - Secure Flask App{% endblock %}

{% block content %}
<div class="blog-container">
    <div class="blog-header">
        <div class="blog-title">
            <h1><i class="fas fa-book-open"></i> Community Stories</h1>
            <p>Inspiring personal experiences and journeys from our community</p>
        </div>
        {% if session.user_id %}
        <a href="{{ url_for('main.create_blog') }}" class="btn btn-primary">
            <i class="fas fa-pen-fancy"></i>
            Share Your Story
        </a>
        {% endif %}
    </div>

    <div class="blog-stats">
        <div class="stat-card">
            <i class="fas fa-book"></i>
            <div>
                <h3>{{ total_blogs }}</h3>
                <p>Published Stories</p>
            </div>
        </div>
        <div class="stat-card">
            <i class="fas fa-users"></i>
            <div>
                <h3>{{ blogs|map(attribute='author_username')|unique|list|length }}</h3>
                <p>Storytellers</p>
            </div>
        </div>
        <div class="stat-card">
            <i class="fas fa-heart"></i>
            <div>
                <h3>{{ blogs|sum(attribute='likes')|length or 0 }}</h3>
                <p>Total Likes</p>
            </div>
        </div>
    </div>

    <div class="blog-content">
        {% if blogs %}
            <div class="blogs-grid">
                {% for blog in blogs %}
                <article class="blog-card">
                    <div class="blog-header-card">
                        <div class="author-info">
                            <div class="author-avatar">
                                {% if blog.get('author_profile_picture') %}
                                    <img src="{{ url_for('static', filename='uploads/' + blog.author_profile_picture) }}" alt="{{ blog.author_name }}">
                                {% else %}
                                    <i class="fas fa-user-circle"></i>
                                {% endif %}
                            </div>
                            <div class="author-details">
                                <h4>{{ blog.author_name }}</h4>
                                <time datetime="{{ blog.created_at.isoformat() }}">
                                    {{ blog.created_at.strftime('%B %d, %Y') }}
                                </time>
                            </div>
                        </div>
                        <div class="blog-stats-mini">
                            <span class="views">
                                <i class="fas fa-eye"></i>
                                {{ blog.views }}
                            </span>
                        </div>
                    </div>

                    {% if blog.image_filename %}
                    <div class="blog-image">
                        <img src="{{ url_for('static', filename='uploads/' + blog.image_filename) }}" alt="{{ blog.title }}">
                    </div>
                    {% endif %}

                    <div class="blog-content-card">
                        <h3><a href="{{ url_for('main.view_blog', blog_id=blog._id) }}">{{ blog.title }}</a></h3>
                        <p>{{ blog.content[:300] }}{% if blog.content|length > 300 %}...{% endif %}</p>
                        
                        {% if blog.tags %}
                        <div class="blog-tags">
                            {% for tag in blog.tags[:3] %}
                                <span class="tag">{{ tag }}</span>
                            {% endfor %}
                            {% if blog.tags|length > 3 %}
                                <span class="tag-more">+{{ blog.tags|length - 3 }} more</span>
                            {% endif %}
                        </div>
                        {% endif %}
                    </div>

                    <div class="blog-footer">
                        <div class="blog-actions">
                            <span class="action-btn">
                                <i class="fas fa-heart"></i>
                                <span>{{ blog.likes|length }}</span>
                            </span>
                        </div>
                        <a href="{{ url_for('main.view_blog', blog_id=blog._id) }}" class="read-more">
                            Read Story <i class="fas fa-arrow-right"></i>
                        </a>
                    </div>
                </article>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if pagination.total > pagination.per_page %}
            <div class="pagination">
                {% if pagination.has_prev %}
                    <a href="{{ url_for('main.view_blogs', page=pagination.prev_num) }}" class="page-btn">
                        <i class="fas fa-chevron-left"></i> Previous
                    </a>
                {% endif %}
                
                {% for page_num in pagination.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != pagination.page %}
                            <a href="{{ url_for('main.view_blogs', page=page_num) }}" class="page-btn">{{ page_num }}</a>
                        {% else %}
                            <span class="page-btn active">{{ page_num }}</span>
                        {% endif %}
                    {% else %}
                        <span class="page-btn disabled">...</span>
                    {% endif %}
                {% endfor %}
                
                {% if pagination.has_next %}
                    <a href="{{ url_for('main.view_blogs', page=pagination.next_num) }}" class="page-btn">
                        Next <i class="fas fa-chevron-right"></i>
                    </a>
                {% endif %}
            </div>
            {% endif %}
        {% else %}
            <div class="empty-state">
                <i class="fas fa-book-open"></i>
                <h3>No stories yet</h3>
                <p>Be the first to share your inspiring story with the community!</p>
                {% if session.user_id %}
                <a href="{{ url_for('main.create_blog') }}" class="btn btn-primary">
                    <i class="fas fa-pen-fancy"></i>
                    Share Your Story
                </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<style>
.blog-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
}

.blogs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.blog-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.blog-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.blog-header-card {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
}

.blog-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.blog-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.blog-card:hover .blog-image img {
    transform: scale(1.05);
}

.blog-content-card {
    padding: 1.5rem;
}

.blog-content-card h3 {
    margin: 0 0 1rem 0;
    color: #333;
    font-size: 1.3rem;
    line-height: 1.4;
}

.blog-content-card h3 a {
    text-decoration: none;
    color: inherit;
    transition: color 0.3s ease;
}

.blog-content-card h3 a:hover {
    color: #667eea;
}

.blog-content-card p {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1rem;
}

.blog-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 1rem;
}

.tag {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.tag-more {
    background: rgba(102, 126, 234, 0.05);
    color: #999;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
}

.blog-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    border-top: 1px solid rgba(102, 126, 234, 0.1);
    background: rgba(102, 126, 234, 0.02);
}

.blog-stats-mini {
    display: flex;
    gap: 1rem;
    align-items: center;
    color: #666;
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .blogs-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .blog-header-card {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .blog-footer {
        flex-direction: column;
        gap: 1rem;
    }
}
</style>
{% endblock %}
