{% extends "base.html" %}

{% block title %}Medical Lessons - MeduLearn{% endblock %}

{% block content %}
<div class="lessons-container">
    <div class="lessons-header">
        <div class="lessons-title">
            <h1><i class="fas fa-graduation-cap"></i> Medical Education Lessons</h1>
            <p>Learn about important health conditions in your preferred language</p>
            <div class="language-selector">
                <span>Choose Language: </span>
                <button class="lang-btn active" data-lang="english">English</button>
                <button class="lang-btn" data-lang="kannada">ಕನ್ನಡ</button>
                <button class="lang-btn" data-lang="hindi">हिंदी</button>
                <button class="lang-btn" data-lang="telugu">తెలుగు</button>
            </div>
        </div>
    </div>

    <div class="learning-modules">
        {% for module in learning_modules %}
        <div class="module-section">
            <div class="module-header">
                <div class="module-icon">
                    <i class="{{ module.icon }}"></i>
                </div>
                <div class="module-info">
                    <h2 class="module-name"
                        data-english="{{ module.name }}"
                        data-kannada="{{ module.name_kannada }}"
                        data-hindi="{{ module.name_hindi }}"
                        data-telugu="{{ module.name_telugu }}">{{ module.name }}</h2>
                    <p class="module-description">{{ module.description }}</p>
                </div>
                {% if module.id == 'anatomy' %}
                <a href="{{ url_for('main.anatomy_lab') }}" class="module-link">
                    <i class="fas fa-arrow-right"></i> Enter Lab
                </a>
                {% endif %}
            </div>

            <div class="modules-grid">
                {% for item in module.modules %}
                <div class="module-card">
                    <div class="card-content">
                        <h3 class="item-name"
                            data-english="{{ item.name }}"
                            data-kannada="{{ item.name_kannada }}"
                            data-hindi="{{ item.name_hindi }}"
                            data-telugu="{{ item.name_telugu }}">{{ item.name }}</h3>
                        <p class="item-description">{{ item.description }}</p>
                        <div class="item-meta">
                            <span class="difficulty {{ item.difficulty.lower() }}">
                                <i class="fas fa-signal"></i> {{ item.difficulty }}
                            </span>
                            <span class="duration">
                                <i class="fas fa-clock"></i> {{ item.duration }}
                            </span>
                        </div>
                    </div>
                    <div class="card-actions">
                        {% if module.id == 'diseases' %}
                        <a href="{{ url_for('main.view_lesson', disease_id=item.id) }}" class="btn btn-primary">
                            <i class="fas fa-play"></i> Start Lesson
                        </a>
                        <a href="{{ url_for('main.lesson_quiz', disease_id=item.id) }}" class="btn btn-secondary">
                            <i class="fas fa-question-circle"></i> Take Quiz
                        </a>
                        {% elif module.id == 'anatomy' %}
                        <a href="{{ url_for('main.view_anatomy_system', system_id=item.id) }}" class="btn btn-primary">
                            <i class="fas fa-cube"></i> 3D Explore
                        </a>
                        <button class="btn btn-secondary" onclick="startVoiceTour('{{ item.id }}')">
                            <i class="fas fa-microphone"></i> Voice Tour
                        </button>
                        {% endif %}
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endfor %}
    </div>

    <div class="features-section">
        <h2><i class="fas fa-star"></i> MeduLearn Features</h2>
        <div class="features-grid">
            <div class="feature-card">
                <i class="fas fa-language"></i>
                <h3>Multilingual Support</h3>
                <p>Content available in English, Kannada, Hindi, and Telugu</p>
            </div>
            <div class="feature-card">
                <i class="fas fa-robot"></i>
                <h3>AI-Powered Chatbot</h3>
                <p>Get instant answers to your medical questions</p>
            </div>
            <div class="feature-card">
                <i class="fas fa-volume-up"></i>
                <h3>Audio Lessons</h3>
                <p>Listen to lessons with text-to-speech in your language</p>
            </div>
            <div class="feature-card">
                <i class="fas fa-certificate"></i>
                <h3>Certificates</h3>
                <p>Earn certificates upon completing lessons and quizzes</p>
            </div>
        </div>
    </div>
</div>

<style>
.lessons-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.lessons-header {
    text-align: center;
    margin-bottom: 3rem;
}

.lessons-title h1 {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 2.5rem;
}

.lessons-title p {
    color: #7f8c8d;
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.language-selector {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
}

.lang-btn {
    padding: 0.5rem 1rem;
    border: 2px solid #3498db;
    background: white;
    color: #3498db;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
}

.lang-btn.active,
.lang-btn:hover {
    background: #3498db;
    color: white;
}

.learning-modules {
    display: grid;
    gap: 4rem;
    margin-bottom: 4rem;
}

.module-section {
    background: white;
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 1px solid #ecf0f1;
}

.module-header {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 2rem;
    padding-bottom: 2rem;
    border-bottom: 2px solid #ecf0f1;
}

.module-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2.5rem;
    flex-shrink: 0;
}

.module-info {
    flex: 1;
}

.module-name {
    color: #2c3e50;
    margin-bottom: 0.5rem;
    font-size: 2rem;
}

.module-description {
    color: #7f8c8d;
    font-size: 1.1rem;
    line-height: 1.6;
    margin: 0;
}

.module-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.module-link:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
    color: white;
    text-decoration: none;
}

.modules-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.module-card {
    background: #f8f9fa;
    border-radius: 15px;
    padding: 2rem;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.module-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
    background: white;
}

.item-name {
    color: #2c3e50;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.item-description {
    color: #7f8c8d;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.item-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 2rem;
    padding: 1rem;
    background: white;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.difficulty {
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.85rem;
    font-weight: 600;
}

.difficulty.beginner {
    background: #d4edda;
    color: #155724;
}

.difficulty.intermediate {
    background: #fff3cd;
    color: #856404;
}

.difficulty.advanced {
    background: #f8d7da;
    color: #721c24;
}

.duration {
    color: #6c757d;
    font-size: 0.9rem;
}

.card-actions {
    display: flex;
    gap: 1rem;
}

.card-actions .btn {
    flex: 1;
    text-align: center;
    padding: 0.75rem;
    border-radius: 8px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-primary {
    background: #3498db;
    color: white;
    border: none;
}

.btn-primary:hover {
    background: #2980b9;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #95a5a6;
    color: white;
    border: none;
}

.btn-secondary:hover {
    background: #7f8c8d;
    transform: translateY(-2px);
}

.features-section {
    background: #f8f9fa;
    padding: 3rem;
    border-radius: 15px;
    margin-top: 3rem;
}

.features-section h2 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 2rem;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.feature-card {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.feature-card i {
    font-size: 2.5rem;
    color: #3498db;
    margin-bottom: 1rem;
}

.feature-card h3 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.feature-card p {
    color: #7f8c8d;
    line-height: 1.6;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const langButtons = document.querySelectorAll('.lang-btn');
    const moduleNames = document.querySelectorAll('.module-name, .item-name');

    langButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Remove active class from all buttons
            langButtons.forEach(btn => btn.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');

            const selectedLang = this.dataset.lang;

            // Update module and item names
            moduleNames.forEach(name => {
                const text = name.dataset[selectedLang];
                if (text) {
                    name.textContent = text;
                }
            });
        });
    });
});

// Voice tour functionality
function startVoiceTour(systemId) {
    const tours = {
        'cardiovascular': {
            'english': 'Welcome to the cardiovascular system. The heart pumps blood through arteries and veins to deliver oxygen throughout your body.',
            'kannada': 'ಹೃದಯರಕ್ತನಾಳ ವ್ಯವಸ್ಥೆಗೆ ಸ್ವಾಗತ. ಹೃದಯವು ನಿಮ್ಮ ದೇಹದಾದ್ಯಂತ ಆಮ್ಲಜನಕವನ್ನು ತಲುಪಿಸಲು ಅಪಧಮನಿಗಳು ಮತ್ತು ಸಿರೆಗಳ ಮೂಲಕ ರಕ್ತವನ್ನು ಪಂಪ್ ಮಾಡುತ್ತದೆ.',
            'hindi': 'हृदय प्रणाली में आपका स्वागत है। हृदय आपके पूरे शरीर में ऑक्सीजन पहुंचाने के लिए धमनियों और नसों के माध्यम से रक्त पंप करता है।',
            'telugu': 'హృదయ వ్యవస్థకు స్వాగతం. హృదయం మీ శరీరమంతటా ఆక్సిజన్‌ను అందించడానికి ధమనులు మరియు సిరల ద్వారా రక్తాన్ని పంప్ చేస్తుంది.'
        },
        'respiratory': {
            'english': 'Welcome to the respiratory system. Your lungs take in oxygen and remove carbon dioxide through breathing.',
            'kannada': 'ಉಸಿರಾಟದ ವ್ಯವಸ್ಥೆಗೆ ಸ್ವಾಗತ. ನಿಮ್ಮ ಶ್ವಾಸಕೋಶಗಳು ಉಸಿರಾಟದ ಮೂಲಕ ಆಮ್ಲಜನಕವನ್ನು ತೆಗೆದುಕೊಳ್ಳುತ್ತವೆ ಮತ್ತು ಇಂಗಾಲದ ಡೈಆಕ್ಸೈಡ್ ಅನ್ನು ತೆಗೆದುಹಾಕುತ್ತವೆ.',
            'hindi': 'श्वसन तंत्र में आपका स्वागत है। आपके फेफड़े सांस लेने के माध्यम से ऑक्सीजन लेते हैं और कार्बन डाइऑक्साइड को हटाते हैं।',
            'telugu': 'శ్వాస వ్యవస్థకు స్వాగతం. మీ ఊపిరితిత్తులు శ్వాస ద్వారా ఆక్సిజన్‌ను తీసుకుంటాయి మరియు కార్బన్ డయాక్సైడ్‌ను తొలగిస్తాయి.'
        },
        'nervous': {
            'english': 'Welcome to the nervous system. The brain and spinal cord control all body functions through electrical signals.',
            'kannada': 'ನರಮಂಡಲಕ್ಕೆ ಸ್ವಾಗತ. ಮೆದುಳು ಮತ್ತು ಬೆನ್ನುಹುರಿಯು ವಿದ್ಯುತ್ ಸಂಕೇತಗಳ ಮೂಲಕ ಎಲ್ಲಾ ದೇಹದ ಕಾರ್ಯಗಳನ್ನು ನಿಯಂತ್ರಿಸುತ್ತದೆ.',
            'hindi': 'तंत्रिका तंत्र में आपका स्वागत है। मस्तिष्क और रीढ़ की हड्डी विद्युत संकेतों के माध्यम से सभी शरीर के कार्यों को नियंत्रित करते हैं।',
            'telugu': 'నాడీ వ్యవస్థకు స్వాగతం. మెదడు మరియు వెన్నుపాము విద్యుత్ సంకేతాల ద్వారా అన్ని శరీర పనులను నియంత్రిస్తాయి.'
        }
    };

    const currentLang = document.querySelector('.lang-btn.active')?.dataset.lang || 'english';
    const text = tours[systemId]?.[currentLang] || tours[systemId]?.['english'] || 'Welcome to the anatomy tour.';

    const utterance = new SpeechSynthesisUtterance(text);

    const langCodes = {
        'english': 'en-US',
        'kannada': 'kn-IN',
        'hindi': 'hi-IN',
        'telugu': 'te-IN'
    };

    utterance.lang = langCodes[currentLang] || 'en-US';
    speechSynthesis.speak(utterance);
}
</script>
{% endblock %}
