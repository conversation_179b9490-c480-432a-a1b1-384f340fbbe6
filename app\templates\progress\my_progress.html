{% extends "base.html" %}

{% block title %}My Learning Progress - MeduLearn{% endblock %}

{% block content %}
<div class="progress-container">
    <div class="progress-header">
        <h1><i class="fas fa-chart-line"></i> My Learning Progress</h1>
        <p>Track your medical education journey</p>
    </div>

    <div class="stats-overview">
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <div class="stat-content">
                <h3>{{ progress.completed_lessons|length }}</h3>
                <p>Lessons Completed</p>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-certificate"></i>
            </div>
            <div class="stat-content">
                <h3>{{ progress.certificates_earned }}</h3>
                <p>Certificates Earned</p>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-clock"></i>
            </div>
            <div class="stat-content">
                <h3>{{ progress.total_study_time }}</h3>
                <p>Minutes Studied</p>
            </div>
        </div>
        
        <div class="stat-card">
            <div class="stat-icon">
                <i class="fas fa-fire"></i>
            </div>
            <div class="stat-content">
                <h3>{{ progress.streak_days }}</h3>
                <p>Day Streak</p>
            </div>
        </div>
    </div>

    <div class="progress-sections">
        <div class="section">
            <h2><i class="fas fa-tasks"></i> Lesson Progress</h2>
            <div class="lessons-progress">
                <div class="lesson-item completed">
                    <div class="lesson-info">
                        <i class="fas fa-heartbeat"></i>
                        <div>
                            <h4>Diabetes</h4>
                            <p>ಮಧುಮೇಹ | मधुमेह | మధుమేహం</p>
                        </div>
                    </div>
                    <div class="lesson-status">
                        <i class="fas fa-check-circle"></i>
                        <span>Completed</span>
                    </div>
                </div>
                
                <div class="lesson-item in-progress">
                    <div class="lesson-info">
                        <i class="fas fa-brain"></i>
                        <div>
                            <h4>Stroke</h4>
                            <p>ಪಾರ್ಶ್ವವಾಯು | स्ट्रोक | స్ట్రోక్</p>
                        </div>
                    </div>
                    <div class="lesson-status">
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 60%"></div>
                        </div>
                        <span>60% Complete</span>
                    </div>
                </div>
                
                <div class="lesson-item not-started">
                    <div class="lesson-info">
                        <i class="fas fa-bug"></i>
                        <div>
                            <h4>Dengue</h4>
                            <p>ಡೆಂಗ್ಯೂ | डेंगू | డెంగ్యూ</p>
                        </div>
                    </div>
                    <div class="lesson-status">
                        <i class="fas fa-play-circle"></i>
                        <span>Not Started</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2><i class="fas fa-question-circle"></i> Quiz Scores</h2>
            <div class="quiz-scores">
                {% for disease, score in progress.quiz_scores.items() %}
                <div class="quiz-item">
                    <div class="quiz-info">
                        <h4>{{ disease.title() }} Quiz</h4>
                        <div class="score-circle">
                            <div class="score-text">{{ score }}%</div>
                        </div>
                    </div>
                    <div class="score-bar">
                        <div class="score-fill" style="width: {{ score }}%"></div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>

        <div class="section">
            <h2><i class="fas fa-award"></i> Certificates</h2>
            <div class="certificates-grid">
                {% for cert in progress.certificates %}
                <div class="certificate-card">
                    <div class="certificate-icon">
                        <i class="fas fa-certificate"></i>
                    </div>
                    <div class="certificate-content">
                        <h4>{{ cert.disease.title() }} Completion Certificate</h4>
                        <p>Score: {{ cert.score }}%</p>
                        <p>Date: {{ cert.date }}</p>
                        <p>ID: {{ cert.certificate_id }}</p>
                    </div>
                    <button class="download-btn">
                        <i class="fas fa-download"></i> Download
                    </button>
                </div>
                {% endfor %}
            </div>
        </div>

        <div class="section">
            <h2><i class="fas fa-calendar-alt"></i> Learning Activity</h2>
            <div class="activity-calendar">
                <div class="calendar-header">
                    <h4>This Week's Activity</h4>
                </div>
                <div class="calendar-days">
                    <div class="day active">Mon</div>
                    <div class="day active">Tue</div>
                    <div class="day active">Wed</div>
                    <div class="day">Thu</div>
                    <div class="day">Fri</div>
                    <div class="day">Sat</div>
                    <div class="day">Sun</div>
                </div>
            </div>
        </div>
    </div>

    <div class="action-buttons">
        <a href="{{ url_for('main.lessons') }}" class="btn btn-primary">
            <i class="fas fa-book-open"></i> Continue Learning
        </a>
        <a href="{{ url_for('main.chatbot') }}" class="btn btn-secondary">
            <i class="fas fa-robot"></i> Ask AI Assistant
        </a>
    </div>
</div>

<style>
.progress-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.progress-header {
    text-align: center;
    margin-bottom: 3rem;
}

.progress-header h1 {
    color: #2c3e50;
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.progress-header p {
    color: #7f8c8d;
    font-size: 1.2rem;
}

.stats-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #3498db, #2980b9);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
}

.stat-content h3 {
    color: #2c3e50;
    font-size: 2rem;
    margin: 0;
}

.stat-content p {
    color: #7f8c8d;
    margin: 0;
    font-weight: 600;
}

.progress-sections {
    display: grid;
    gap: 2rem;
}

.section {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.section h2 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.lessons-progress {
    display: grid;
    gap: 1rem;
}

.lesson-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-radius: 10px;
    border-left: 4px solid;
}

.lesson-item.completed {
    background: #d4edda;
    border-left-color: #27ae60;
}

.lesson-item.in-progress {
    background: #fff3cd;
    border-left-color: #f39c12;
}

.lesson-item.not-started {
    background: #f8f9fa;
    border-left-color: #95a5a6;
}

.lesson-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.lesson-info i {
    font-size: 1.5rem;
    color: #e74c3c;
}

.lesson-info h4 {
    margin: 0;
    color: #2c3e50;
}

.lesson-info p {
    margin: 0;
    color: #7f8c8d;
    font-size: 0.9rem;
}

.lesson-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.progress-bar {
    width: 100px;
    height: 8px;
    background: #ecf0f1;
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: #f39c12;
    transition: width 0.3s ease;
}

.quiz-scores {
    display: grid;
    gap: 1rem;
}

.quiz-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    background: #f8f9fa;
    border-radius: 10px;
}

.quiz-info {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.score-circle {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: #3498db;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}

.score-bar {
    width: 200px;
    height: 10px;
    background: #ecf0f1;
    border-radius: 5px;
    overflow: hidden;
}

.score-fill {
    height: 100%;
    background: #27ae60;
    transition: width 0.3s ease;
}

.certificates-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.certificate-card {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    color: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
}

.certificate-icon i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.certificate-content h4 {
    margin-bottom: 1rem;
}

.certificate-content p {
    margin: 0.25rem 0;
    opacity: 0.9;
}

.download-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid white;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    cursor: pointer;
    margin-top: 1rem;
    transition: all 0.3s ease;
}

.download-btn:hover {
    background: white;
    color: #f39c12;
}

.activity-calendar {
    text-align: center;
}

.calendar-header h4 {
    color: #2c3e50;
    margin-bottom: 1rem;
}

.calendar-days {
    display: flex;
    justify-content: center;
    gap: 1rem;
}

.day {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #ecf0f1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: #7f8c8d;
}

.day.active {
    background: #27ae60;
    color: white;
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 3rem;
}

.action-buttons .btn {
    padding: 1rem 2rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-primary {
    background: #3498db;
    color: white;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}
</style>
{% endblock %}
