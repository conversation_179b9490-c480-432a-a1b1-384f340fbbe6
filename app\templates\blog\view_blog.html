{% extends "base.html" %}

{% block title %}{{ blog.title }} - Community Stories{% endblock %}

{% block content %}
<div class="blog-view-container">
    <div class="blog-navigation">
        <a href="{{ url_for('main.view_blogs') }}" class="back-btn">
            <i class="fas fa-arrow-left"></i>
            Back to Stories
        </a>
    </div>

    <article class="blog-detail">
        <header class="blog-header">
            <div class="author-section">
                <div class="author-avatar">
                    {% if author_profile_picture %}
                        <img src="{{ url_for('static', filename='uploads/' + author_profile_picture) }}" alt="{{ blog.author_name }}">
                    {% else %}
                        <i class="fas fa-user-circle"></i>
                    {% endif %}
                </div>
                <div class="author-info">
                    <h3>{{ blog.author_name }}</h3>
                    <time datetime="{{ blog.created_at.isoformat() }}">
                        {{ blog.created_at.strftime('%B %d, %Y') }}
                    </time>
                </div>
            </div>
            
            <div class="blog-stats">
                <div class="stat-item">
                    <i class="fas fa-eye"></i>
                    <span>{{ blog.views }} views</span>
                </div>
                <button class="stat-item like-btn {{ 'liked' if current_user_liked else '' }}" data-blog-id="{{ blog._id }}">
                    <i class="fas fa-heart"></i>
                    <span id="likes-count">{{ blog.likes|length }}</span>
                    <span>likes</span>
                </button>
            </div>
        </header>

        <div class="blog-content">
            <h1 class="blog-title">{{ blog.title }}</h1>
            
            {% if blog.image_filename %}
            <div class="blog-image">
                <img src="{{ url_for('static', filename='uploads/' + blog.image_filename) }}" alt="{{ blog.title }}">
            </div>
            {% endif %}
            
            <div class="blog-text">
                {{ blog.content|nl2br|safe }}
            </div>
            
            {% if blog.tags %}
            <div class="blog-tags">
                <h4><i class="fas fa-tags"></i> Tags</h4>
                <div class="tags-list">
                    {% for tag in blog.tags %}
                        <span class="tag">{{ tag }}</span>
                    {% endfor %}
                </div>
            </div>
            {% endif %}
        </div>
    </article>

    <div class="blog-engagement">
        <div class="engagement-actions">
            {% if session.user_id %}
                <button class="engagement-btn like-btn-large {{ 'liked' if current_user_liked else '' }}" data-blog-id="{{ blog._id }}">
                    <i class="fas fa-heart"></i>
                    <span>{{ 'Unlike' if current_user_liked else 'Like' }} this story</span>
                </button>
                <a href="{{ url_for('main.create_blog') }}" class="engagement-btn">
                    <i class="fas fa-pen-fancy"></i>
                    <span>Share your story</span>
                </a>
            {% else %}
                <a href="{{ url_for('main.login') }}" class="engagement-btn">
                    <i class="fas fa-heart"></i>
                    <span>Login to like this story</span>
                </a>
            {% endif %}
        </div>
    </div>

    <div class="related-stories">
        <h3><i class="fas fa-book-open"></i> More Stories</h3>
        <div class="related-actions">
            <a href="{{ url_for('main.view_blogs') }}" class="btn btn-primary">
                <i class="fas fa-arrow-right"></i>
                Explore All Stories
            </a>
            {% if session.user_id %}
            <a href="{{ url_for('main.create_blog') }}" class="btn btn-secondary">
                <i class="fas fa-plus"></i>
                Share Your Story
            </a>
            {% endif %}
        </div>
    </div>
</div>

<style>
.blog-view-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 1rem;
}

.blog-navigation {
    margin-bottom: 2rem;
}

.back-btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background: rgba(255, 255, 255, 0.95);
    color: #667eea;
    text-decoration: none;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    font-weight: 500;
}

.back-btn:hover {
    background: #667eea;
    color: white;
    transform: translateX(-3px);
}

.blog-detail {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 2rem;
}

.blog-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    border-bottom: 1px solid rgba(102, 126, 234, 0.1);
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.author-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
}

.author-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.author-avatar i {
    font-size: 2.5rem;
    color: #ccc;
}

.author-info h3 {
    margin: 0;
    color: #333;
    font-size: 1.2rem;
}

.author-info time {
    color: #666;
    font-size: 0.9rem;
}

.blog-stats {
    display: flex;
    gap: 2rem;
    align-items: center;
}

.blog-title {
    font-size: 2.5rem;
    color: #333;
    margin-bottom: 2rem;
    line-height: 1.3;
    font-weight: 700;
}

.blog-content {
    padding: 2rem;
}

.blog-image {
    margin-bottom: 2rem;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.blog-image img {
    width: 100%;
    height: auto;
    display: block;
}

.blog-text {
    font-size: 1.2rem;
    line-height: 1.8;
    color: #444;
    font-family: 'Georgia', 'Times New Roman', serif;
    margin-bottom: 2rem;
}

.blog-tags {
    padding-top: 2rem;
    border-top: 1px solid rgba(102, 126, 234, 0.1);
}

.blog-tags h4 {
    color: #667eea;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.tags-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.tag {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
    color: #667eea;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    border: 1px solid rgba(102, 126, 234, 0.2);
}

.blog-engagement {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.engagement-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.engagement-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 2rem;
    border: 2px solid #667eea;
    border-radius: 50px;
    background: transparent;
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.engagement-btn:hover,
.engagement-btn.liked {
    background: #667eea;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.like-btn-large.liked {
    background: #dc3545;
    border-color: #dc3545;
    color: white;
}

.related-stories {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.related-stories h3 {
    color: #667eea;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
}

.related-actions {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

@media (max-width: 768px) {
    .blog-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .blog-title {
        font-size: 2rem;
    }
    
    .blog-text {
        font-size: 1.1rem;
    }
    
    .engagement-actions,
    .related-actions {
        flex-direction: column;
        align-items: center;
    }
    
    .engagement-btn {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }
}
</style>

<script>
// Like functionality for both buttons
document.querySelectorAll('.like-btn, .like-btn-large').forEach(btn => {
    btn.addEventListener('click', async function() {
        const blogId = this.dataset.blogId;
        try {
            const response = await fetch(`/blog/${blogId}/like`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });
            const data = await response.json();
            if (data.success) {
                // Update likes count
                document.getElementById('likes-count').textContent = data.likes_count;
                
                // Update button states
                document.querySelectorAll('.like-btn, .like-btn-large').forEach(button => {
                    button.classList.toggle('liked', data.liked);
                    if (button.classList.contains('like-btn-large')) {
                        const span = button.querySelector('span');
                        span.textContent = data.liked ? 'Unlike this story' : 'Like this story';
                    }
                });
            }
        } catch (error) {
            console.error('Error:', error);
        }
    });
});
</script>
{% endblock %}
