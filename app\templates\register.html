{% extends "base.html" %}

{% block title %}Register | MeduLearn - AI-Powered Medical Education{% endblock %}

{% block content %}
<div style="min-height: calc(100vh - 80px); display: flex; align-items: center; justify-content: center; padding: 2rem 1rem;">
    <div class="container" style="max-width: 600px; margin: 0;">
        <div style="text-align: center; margin-bottom: 2.5rem;">
            <div style="display: inline-flex; align-items: center; justify-content: center; width: 80px; height: 80px; background: var(--secondary-color); border-radius: 50%; margin-bottom: 1.5rem; box-shadow: var(--shadow-lg);">
                <i class="fas fa-user-plus" style="font-size: 2rem; color: var(--text-inverse);"></i>
            </div>
            <h1 style="font-size: 2.2rem; font-weight: 700; color: var(--secondary-color); margin-bottom: 0.75rem;">Create Account</h1>
            <p style="color: var(--text-secondary); font-size: 1.1rem; font-weight: 400;">Join our AI-powered medical education platform</p>
        </div>

        <form method="POST" action="{{ url_for('main.register') }}">
            {{ form.hidden_tag() }}

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; margin-bottom: 1.5rem;">
                <div class="form-group">
                    <label class="form-label" for="full_name">
                        <i class="fas fa-id-card" style="margin-right: 0.75rem; color: var(--secondary-color);"></i>
                        Full Name
                    </label>
                    {{ form.full_name(class="form-control", id="full_name", placeholder="Enter your full name") }}
                    {% for error in form.full_name.errors %}
                        <div class="error">
                            {{ error }}
                        </div>
                    {% endfor %}
                </div>

                <div class="form-group">
                    <label class="form-label" for="username">
                        <i class="fas fa-user" style="margin-right: 0.75rem; color: var(--secondary-color);"></i>
                        Username
                    </label>
                    {{ form.username(class="form-control", id="username", placeholder="Choose a username") }}
                    {% for error in form.username.errors %}
                        <div class="error">
                            {{ error }}
                        </div>
                    {% endfor %}
                </div>
            </div>

            <div class="form-group">
                <label class="form-label" for="email">
                    <i class="fas fa-envelope" style="margin-right: 0.75rem; color: var(--secondary-color);"></i>
                    Email Address
                </label>
                {{ form.email(class="form-control", id="email", placeholder="Enter your email address") }}
                {% for error in form.email.errors %}
                    <div class="error">
                        {{ error }}
                    </div>
                {% endfor %}
            </div>

            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem; margin-bottom: 1.5rem;">
                <div class="form-group">
                    <label class="form-label" for="password">
                        <i class="fas fa-lock" style="margin-right: 0.75rem; color: var(--secondary-color);"></i>
                        Password
                    </label>
                    <div style="position: relative;">
                        {{ form.password(class="form-control", id="password", placeholder="Create a password") }}
                        <button type="button" onclick="togglePassword('password')" style="position: absolute; right: 1rem; top: 50%; transform: translateY(-50%); background: none; border: none; color: var(--text-secondary); cursor: pointer; transition: all 0.3s ease; padding: 0.5rem;">
                            <i class="fas fa-eye" id="password-eye"></i>
                        </button>
                    </div>
                    {% for error in form.password.errors %}
                        <div class="error">
                            {{ error }}
                        </div>
                    {% endfor %}
                </div>

                <div class="form-group">
                    <label class="form-label" for="confirm_password">
                        <i class="fas fa-lock" style="margin-right: 0.75rem; color: var(--secondary-color);"></i>
                        Confirm Password
                    </label>
                    <div style="position: relative;">
                        {{ form.confirm_password(class="form-control", id="confirm_password", placeholder="Confirm your password") }}
                        <button type="button" onclick="togglePassword('confirm_password')" style="position: absolute; right: 1rem; top: 50%; transform: translateY(-50%); background: none; border: none; color: var(--text-secondary); cursor: pointer; transition: all 0.3s ease; padding: 0.5rem;">
                            <i class="fas fa-eye" id="confirm_password-eye"></i>
                        </button>
                    </div>
                    {% for error in form.confirm_password.errors %}
                        <div class="error">
                            {{ error }}
                        </div>
                    {% endfor %}
                </div>
            </div>

            <div style="margin-bottom: 1.5rem;">
                <div style="background: var(--bg-glass); border-radius: 12px; padding: 1rem; border: 1px solid var(--border-secondary);">
                    <div style="display: flex; align-items: center; gap: 0.75rem; margin-bottom: 0.5rem;">
                        <i class="fas fa-shield-alt" style="color: var(--secondary-color);"></i>
                        <span style="color: var(--text-primary); font-weight: 600; font-size: 0.9rem;">Password Strength</span>
                    </div>
                    <div style="background: var(--bg-secondary); height: 6px; border-radius: 3px; overflow: hidden;">
                        <div id="strength-fill" style="height: 100%; width: 0%; background: var(--error); transition: all 0.3s ease; border-radius: 3px;"></div>
                    </div>
                    <span id="strength-text" style="color: var(--text-secondary); font-size: 0.8rem; margin-top: 0.5rem; display: block;">Enter a password to see strength</span>
                </div>
            </div>

            <div class="form-group">
                {{ form.submit(class="btn btn-primary", style="width: 100%;") }}
            </div>
        </form>

        <div style="text-align: center; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid var(--border-secondary);">
            <p style="color: var(--text-secondary); font-size: 0.95rem;">
                Already have an account?
                <a href="{{ url_for('main.login') }}" style="color: var(--primary-color); text-decoration: none; font-weight: 600; transition: all 0.3s ease;">
                    Sign in here <i class="fas fa-arrow-right" style="margin-left: 0.5rem;"></i>
                </a>
            </p>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const eye = document.getElementById(fieldId + '-eye');

    if (field.type === 'password') {
        field.type = 'text';
        eye.className = 'fas fa-eye-slash';
    } else {
        field.type = 'password';
        eye.className = 'fas fa-eye';
    }
}

// Password strength checker
function checkPasswordStrength(password) {
    let strength = 0;
    let feedback = '';

    if (password.length >= 8) strength += 1;
    if (password.match(/[a-z]/)) strength += 1;
    if (password.match(/[A-Z]/)) strength += 1;
    if (password.match(/[0-9]/)) strength += 1;
    if (password.match(/[^a-zA-Z0-9]/)) strength += 1;

    const strengthFill = document.getElementById('strength-fill');
    const strengthText = document.getElementById('strength-text');

    switch (strength) {
        case 0:
        case 1:
            strengthFill.style.width = '20%';
            strengthFill.style.backgroundColor = '#ef4444';
            feedback = 'Very Weak';
            break;
        case 2:
            strengthFill.style.width = '40%';
            strengthFill.style.backgroundColor = '#f59e0b';
            feedback = 'Weak';
            break;
        case 3:
            strengthFill.style.width = '60%';
            strengthFill.style.backgroundColor = '#eab308';
            feedback = 'Fair';
            break;
        case 4:
            strengthFill.style.width = '80%';
            strengthFill.style.backgroundColor = '#22c55e';
            feedback = 'Good';
            break;
        case 5:
            strengthFill.style.width = '100%';
            strengthFill.style.backgroundColor = '#10b981';
            feedback = 'Strong';
            break;
    }

    strengthText.textContent = feedback;
    strengthText.style.color = strengthFill.style.backgroundColor;
}

// Add event listeners
document.addEventListener('DOMContentLoaded', function() {
    const passwordField = document.getElementById('password');
    if (passwordField) {
        passwordField.addEventListener('input', function() {
            checkPasswordStrength(this.value);
        });
    }
});
</script>
{% endblock %}