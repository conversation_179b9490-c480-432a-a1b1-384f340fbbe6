# Secure Flask Web Application

This project is a secure web application built using Flask, featuring user registration, login, and a personalized dashboard. It utilizes MongoDB for data storage and retrieval, ensuring that user passwords are hashed for security.

## Features

- User registration with email and password
- User login with session management
- Personalized dashboard displaying user-specific data
- Secure password storage using hashing
- Error handling for various application errors

## Technologies Used

- Flask: A lightweight WSGI web application framework
- Flask-PyMongo: A Flask extension for MongoDB integration
- Flask-WTF: An extension for handling web forms with validation
- Werkzeug: A comprehensive WSGI web application library

## Installation

1. Clone the repository:
   ```
   git clone <repository-url>
   cd secure-flask-app
   ```

2. Create a virtual environment:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows use `venv\Scripts\activate`
   ```

3. Install the required packages:
   ```
   pip install -r requirements.txt
   ```

4. Configure your MongoDB connection in `config.py`.

## Running the Application

To run the application, execute the following command:
```
python run.py
```

The application will be accessible at `http://127.0.0.1:5000`.

## Usage

- Navigate to the registration page to create a new account.
- Log in to access your personalized dashboard.
- Use the dashboard to view and manage your data.

## Contributing

Contributions are welcome! Please open an issue or submit a pull request for any enhancements or bug fixes.

## License

This project is licensed under the MIT License. See the LICENSE file for details.