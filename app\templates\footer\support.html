{% extends "base.html" %}

{% block title %}Support Center | MeduLearn{% endblock %}

{% block content %}
<!-- Professional Page Header -->
<div class="page-header">
    <div class="page-header-content">
        <div class="breadcrumb">
            <a href="{{ url_for('main.dashboard') }}">
                <i class="fas fa-home"></i> Dashboard
            </a>
            <span class="breadcrumb-separator">/</span>
            <span>Support</span>
        </div>
        <h1 class="page-title">Support Center</h1>
        <p class="page-subtitle">
            Find help, tutorials, and resources to make the most of MeduLearn
        </p>
    </div>
</div>

<!-- Support Content -->
<div class="section">
    <div class="section-content">
        
        <!-- Quick Help Options -->
        <div class="section-header">
            <h2 class="section-title">How can we help you?</h2>
            <p class="section-subtitle">Choose from the options below to get started</p>
        </div>

        <div class="grid grid-cols-3">
            <div class="card">
                <div class="card-body" style="text-align: center;">
                    <div style="width: 64px; height: 64px; border-radius: 50%; background: var(--primary-color); display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; color: white; font-size: 1.5rem;">
                        <i class="fas fa-book-open"></i>
                    </div>
                    <h3 style="color: var(--gray-900); margin-bottom: 0.5rem; font-size: 1.125rem; font-weight: 600;">Getting Started</h3>
                    <p style="color: var(--gray-600); font-size: 0.875rem; line-height: 1.5; margin-bottom: 1rem;">
                        New to MeduLearn? Learn the basics and start your medical education journey
                    </p>
                    <button class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-play"></i> Start Tutorial
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-body" style="text-align: center;">
                    <div style="width: 64px; height: 64px; border-radius: 50%; background: var(--secondary-color); display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; color: white; font-size: 1.5rem;">
                        <i class="fas fa-tools"></i>
                    </div>
                    <h3 style="color: var(--gray-900); margin-bottom: 0.5rem; font-size: 1.125rem; font-weight: 600;">Technical Support</h3>
                    <p style="color: var(--gray-600); font-size: 0.875rem; line-height: 1.5; margin-bottom: 1rem;">
                        Having technical issues? Get help with platform features and troubleshooting
                    </p>
                    <button class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-headset"></i> Get Help
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-body" style="text-align: center;">
                    <div style="width: 64px; height: 64px; border-radius: 50%; background: var(--info); display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; color: white; font-size: 1.5rem;">
                        <i class="fas fa-question-circle"></i>
                    </div>
                    <h3 style="color: var(--gray-900); margin-bottom: 0.5rem; font-size: 1.125rem; font-weight: 600;">FAQ</h3>
                    <p style="color: var(--gray-600); font-size: 0.875rem; line-height: 1.5; margin-bottom: 1rem;">
                        Find answers to frequently asked questions about MeduLearn features
                    </p>
                    <button class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-search"></i> Browse FAQ
                    </button>
                </div>
            </div>
        </div>

        <!-- Popular Help Topics -->
        <div class="section-header" style="margin-top: 3rem;">
            <h2 class="section-title">Popular Help Topics</h2>
            <p class="section-subtitle">Most searched support topics</p>
        </div>

        <div class="grid grid-cols-2">
            <!-- Account & Profile -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-user-cog" style="color: var(--primary-color);"></i>
                        Account & Profile
                    </div>
                </div>
                <div class="card-body">
                    <div style="display: grid; gap: 0.75rem;">
                        <a href="#" style="display: flex; align-items: center; gap: 0.5rem; color: var(--gray-600); text-decoration: none; padding: 0.5rem; border-radius: 4px; transition: background 0.2s ease;">
                            <i class="fas fa-chevron-right" style="color: var(--gray-400); font-size: 0.75rem;"></i>
                            <span>How to update my profile information</span>
                        </a>
                        <a href="#" style="display: flex; align-items: center; gap: 0.5rem; color: var(--gray-600); text-decoration: none; padding: 0.5rem; border-radius: 4px; transition: background 0.2s ease;">
                            <i class="fas fa-chevron-right" style="color: var(--gray-400); font-size: 0.75rem;"></i>
                            <span>Changing password and security settings</span>
                        </a>
                        <a href="#" style="display: flex; align-items: center; gap: 0.5rem; color: var(--gray-600); text-decoration: none; padding: 0.5rem; border-radius: 4px; transition: background 0.2s ease;">
                            <i class="fas fa-chevron-right" style="color: var(--gray-400); font-size: 0.75rem;"></i>
                            <span>Managing language preferences</span>
                        </a>
                        <a href="#" style="display: flex; align-items: center; gap: 0.5rem; color: var(--gray-600); text-decoration: none; padding: 0.5rem; border-radius: 4px; transition: background 0.2s ease;">
                            <i class="fas fa-chevron-right" style="color: var(--gray-400); font-size: 0.75rem;"></i>
                            <span>Deleting or deactivating account</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Learning Features -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-graduation-cap" style="color: var(--secondary-color);"></i>
                        Learning Features
                    </div>
                </div>
                <div class="card-body">
                    <div style="display: grid; gap: 0.75rem;">
                        <a href="#" style="display: flex; align-items: center; gap: 0.5rem; color: var(--gray-600); text-decoration: none; padding: 0.5rem; border-radius: 4px; transition: background 0.2s ease;">
                            <i class="fas fa-chevron-right" style="color: var(--gray-400); font-size: 0.75rem;"></i>
                            <span>Using the 3D Anatomy Lab</span>
                        </a>
                        <a href="#" style="display: flex; align-items: center; gap: 0.5rem; color: var(--gray-600); text-decoration: none; padding: 0.5rem; border-radius: 4px; transition: background 0.2s ease;">
                            <i class="fas fa-chevron-right" style="color: var(--gray-400); font-size: 0.75rem;"></i>
                            <span>Voice control and AI assistant</span>
                        </a>
                        <a href="#" style="display: flex; align-items: center; gap: 0.5rem; color: var(--gray-600); text-decoration: none; padding: 0.5rem; border-radius: 4px; transition: background 0.2s ease;">
                            <i class="fas fa-chevron-right" style="color: var(--gray-400); font-size: 0.75rem;"></i>
                            <span>Tracking learning progress</span>
                        </a>
                        <a href="#" style="display: flex; align-items: center; gap: 0.5rem; color: var(--gray-600); text-decoration: none; padding: 0.5rem; border-radius: 4px; transition: background 0.2s ease;">
                            <i class="fas fa-chevron-right" style="color: var(--gray-400); font-size: 0.75rem;"></i>
                            <span>Downloading content for offline use</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Technical Issues -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-bug" style="color: var(--warning);"></i>
                        Technical Issues
                    </div>
                </div>
                <div class="card-body">
                    <div style="display: grid; gap: 0.75rem;">
                        <a href="#" style="display: flex; align-items: center; gap: 0.5rem; color: var(--gray-600); text-decoration: none; padding: 0.5rem; border-radius: 4px; transition: background 0.2s ease;">
                            <i class="fas fa-chevron-right" style="color: var(--gray-400); font-size: 0.75rem;"></i>
                            <span>Platform not loading or slow performance</span>
                        </a>
                        <a href="#" style="display: flex; align-items: center; gap: 0.5rem; color: var(--gray-600); text-decoration: none; padding: 0.5rem; border-radius: 4px; transition: background 0.2s ease;">
                            <i class="fas fa-chevron-right" style="color: var(--gray-400); font-size: 0.75rem;"></i>
                            <span>Audio or video not working</span>
                        </a>
                        <a href="#" style="display: flex; align-items: center; gap: 0.5rem; color: var(--gray-600); text-decoration: none; padding: 0.5rem; border-radius: 4px; transition: background 0.2s ease;">
                            <i class="fas fa-chevron-right" style="color: var(--gray-400); font-size: 0.75rem;"></i>
                            <span>3D models not displaying correctly</span>
                        </a>
                        <a href="#" style="display: flex; align-items: center; gap: 0.5rem; color: var(--gray-600); text-decoration: none; padding: 0.5rem; border-radius: 4px; transition: background 0.2s ease;">
                            <i class="fas fa-chevron-right" style="color: var(--gray-400); font-size: 0.75rem;"></i>
                            <span>Mobile app issues</span>
                        </a>
                    </div>
                </div>
            </div>

            <!-- AR & Voice Features -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-vr-cardboard" style="color: var(--accent-color);"></i>
                        AR & Voice Features
                    </div>
                </div>
                <div class="card-body">
                    <div style="display: grid; gap: 0.75rem;">
                        <a href="#" style="display: flex; align-items: center; gap: 0.5rem; color: var(--gray-600); text-decoration: none; padding: 0.5rem; border-radius: 4px; transition: background 0.2s ease;">
                            <i class="fas fa-chevron-right" style="color: var(--gray-400); font-size: 0.75rem;"></i>
                            <span>Setting up AR mode</span>
                        </a>
                        <a href="#" style="display: flex; align-items: center; gap: 0.5rem; color: var(--gray-600); text-decoration: none; padding: 0.5rem; border-radius: 4px; transition: background 0.2s ease;">
                            <i class="fas fa-chevron-right" style="color: var(--gray-400); font-size: 0.75rem;"></i>
                            <span>Voice commands not working</span>
                        </a>
                        <a href="#" style="display: flex; align-items: center; gap: 0.5rem; color: var(--gray-600); text-decoration: none; padding: 0.5rem; border-radius: 4px; transition: background 0.2s ease;">
                            <i class="fas fa-chevron-right" style="color: var(--gray-400); font-size: 0.75rem;"></i>
                            <span>Microphone permissions</span>
                        </a>
                        <a href="#" style="display: flex; align-items: center; gap: 0.5rem; color: var(--gray-600); text-decoration: none; padding: 0.5rem; border-radius: 4px; transition: background 0.2s ease;">
                            <i class="fas fa-chevron-right" style="color: var(--gray-400); font-size: 0.75rem;"></i>
                            <span>Compatible devices for AR</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Support -->
        <div class="section-header" style="margin-top: 3rem;">
            <h2 class="section-title">Still Need Help?</h2>
            <p class="section-subtitle">Our support team is here to assist you</p>
        </div>

        <div class="grid grid-cols-3">
            <div class="card">
                <div class="card-body" style="text-align: center;">
                    <i class="fas fa-envelope" style="color: var(--primary-color); font-size: 2rem; margin-bottom: 1rem;"></i>
                    <h4 style="color: var(--gray-800); margin-bottom: 0.5rem;">Email Support</h4>
                    <p style="color: var(--gray-600); font-size: 0.875rem; margin-bottom: 1rem;">
                        Get detailed help via email. Response within 24 hours.
                    </p>
                    <a href="mailto:<EMAIL>" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-envelope"></i> Email Us
                    </a>
                </div>
            </div>

            <div class="card">
                <div class="card-body" style="text-align: center;">
                    <i class="fas fa-comments" style="color: var(--secondary-color); font-size: 2rem; margin-bottom: 1rem;"></i>
                    <h4 style="color: var(--gray-800); margin-bottom: 0.5rem;">Live Chat</h4>
                    <p style="color: var(--gray-600); font-size: 0.875rem; margin-bottom: 1rem;">
                        Chat with our support team in real-time during business hours.
                    </p>
                    <button class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-comments"></i> Start Chat
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-body" style="text-align: center;">
                    <i class="fas fa-phone" style="color: var(--info); font-size: 2rem; margin-bottom: 1rem;"></i>
                    <h4 style="color: var(--gray-800); margin-bottom: 0.5rem;">Phone Support</h4>
                    <p style="color: var(--gray-600); font-size: 0.875rem; margin-bottom: 1rem;">
                        Call us for urgent issues. Available Mon-Fri, 9 AM - 6 PM IST.
                    </p>
                    <a href="tel:+91-80-XXXX-XXXX" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-phone"></i> Call Now
                    </a>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="card" style="margin-top: 2rem;">
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-server" style="color: var(--success);"></i>
                    System Status
                </div>
                <div class="card-subtitle">Current status of MeduLearn services</div>
            </div>
            <div class="card-body">
                <div style="display: grid; gap: 1rem;">
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fas fa-circle" style="color: var(--success); font-size: 0.75rem;"></i>
                            <span style="color: var(--gray-700);">Platform Services</span>
                        </div>
                        <span style="color: var(--success); font-weight: 500; font-size: 0.875rem;">Operational</span>
                    </div>
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fas fa-circle" style="color: var(--success); font-size: 0.75rem;"></i>
                            <span style="color: var(--gray-700);">3D Anatomy Lab</span>
                        </div>
                        <span style="color: var(--success); font-weight: 500; font-size: 0.875rem;">Operational</span>
                    </div>
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fas fa-circle" style="color: var(--success); font-size: 0.75rem;"></i>
                            <span style="color: var(--gray-700);">AI Assistant</span>
                        </div>
                        <span style="color: var(--success); font-weight: 500; font-size: 0.875rem;">Operational</span>
                    </div>
                    <div style="display: flex; align-items: center; justify-content: space-between;">
                        <div style="display: flex; align-items: center; gap: 0.75rem;">
                            <i class="fas fa-circle" style="color: var(--warning); font-size: 0.75rem;"></i>
                            <span style="color: var(--gray-700);">Voice Recognition</span>
                        </div>
                        <span style="color: var(--warning); font-weight: 500; font-size: 0.875rem;">Maintenance</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
