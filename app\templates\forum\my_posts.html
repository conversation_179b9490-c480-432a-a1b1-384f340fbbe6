{% extends "base.html" %}

{% block title %}My Posts - Secure Flask App{% endblock %}

{% block content %}
<div class="forum-container">
    <div class="forum-header">
        <div class="forum-title">
            <h1><i class="fas fa-file-alt"></i> My Posts</h1>
            <p>Manage and view all your forum posts</p>
        </div>
        <a href="{{ url_for('main.create_post') }}" class="btn btn-primary">
            <i class="fas fa-plus"></i>
            Create New Post
        </a>
    </div>

    <div class="forum-stats">
        <div class="stat-card">
            <i class="fas fa-file-alt"></i>
            <div>
                <h3>{{ pagination.total }}</h3>
                <p>Your Posts</p>
            </div>
        </div>
        <div class="stat-card">
            <i class="fas fa-eye"></i>
            <div>
                <h3>{{ posts|sum(attribute='views') or 0 }}</h3>
                <p>Total Views</p>
            </div>
        </div>
        <div class="stat-card">
            <i class="fas fa-heart"></i>
            <div>
                <h3>{{ posts|sum(attribute='likes')|length or 0 }}</h3>
                <p>Total Likes</p>
            </div>
        </div>
    </div>

    <div class="forum-content">
        {% if posts %}
            <div class="posts-grid">
                {% for post in posts %}
                <article class="post-card">
                    <div class="post-header">
                        <div class="author-info">
                            <div class="author-details">
                                <h4>{{ post.author_username }}</h4>
                                <time datetime="{{ post.created_at.isoformat() }}">
                                    {{ post.created_at.strftime('%B %d, %Y at %I:%M %p') }}
                                </time>
                                {% if post.updated_at != post.created_at %}
                                    <small class="edited-note">
                                        <i class="fas fa-edit"></i>
                                        Edited {{ post.updated_at.strftime('%B %d, %Y') }}
                                    </small>
                                {% endif %}
                            </div>
                        </div>
                        <div class="post-stats">
                            <span class="views">
                                <i class="fas fa-eye"></i>
                                {{ post.views }}
                            </span>
                        </div>
                    </div>

                    {% if post.image_filename %}
                    <div class="post-image">
                        <img src="{{ url_for('static', filename='uploads/' + post.image_filename) }}" alt="{{ post.title }}">
                    </div>
                    {% endif %}

                    <div class="post-content">
                        <h3><a href="{{ url_for('main.view_post', post_id=post._id) }}">{{ post.title }}</a></h3>
                        <p>{{ post.content[:200] }}{% if post.content|length > 200 %}...{% endif %}</p>
                    </div>

                    <div class="post-footer">
                        <div class="post-actions">
                            <span class="action-btn">
                                <i class="fas fa-heart"></i>
                                <span>{{ post.likes|length }}</span>
                            </span>
                            <span class="action-btn">
                                <i class="fas fa-comment"></i>
                                <span>{{ post.comment_count or 0 }}</span>
                            </span>
                        </div>
                        <div class="post-management">
                            <a href="{{ url_for('main.view_post', post_id=post._id) }}" class="read-more">
                                View <i class="fas fa-arrow-right"></i>
                            </a>
                            <a href="{{ url_for('main.edit_post', post_id=post._id) }}" class="edit-btn">
                                <i class="fas fa-edit"></i>
                                Edit
                            </a>
                            <button class="delete-btn" onclick="confirmDelete('{{ post._id }}', '{{ post.title }}')">
                                <i class="fas fa-trash"></i>
                                Delete
                            </button>
                        </div>
                    </div>
                </article>
                {% endfor %}
            </div>

            <!-- Pagination -->
            {% if pagination.total > pagination.per_page %}
            <div class="pagination">
                {% if pagination.has_prev %}
                    <a href="{{ url_for('main.my_posts', page=pagination.prev_num) }}" class="page-btn">
                        <i class="fas fa-chevron-left"></i> Previous
                    </a>
                {% endif %}
                
                {% for page_num in pagination.iter_pages() %}
                    {% if page_num %}
                        {% if page_num != pagination.page %}
                            <a href="{{ url_for('main.my_posts', page=page_num) }}" class="page-btn">{{ page_num }}</a>
                        {% else %}
                            <span class="page-btn active">{{ page_num }}</span>
                        {% endif %}
                    {% else %}
                        <span class="page-btn disabled">...</span>
                    {% endif %}
                {% endfor %}
                
                {% if pagination.has_next %}
                    <a href="{{ url_for('main.my_posts', page=pagination.next_num) }}" class="page-btn">
                        Next <i class="fas fa-chevron-right"></i>
                    </a>
                {% endif %}
            </div>
            {% endif %}
        {% else %}
            <div class="empty-state">
                <i class="fas fa-file-alt"></i>
                <h3>No posts yet</h3>
                <p>You haven't created any posts yet. Share your thoughts with the community!</p>
                <a href="{{ url_for('main.create_post') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i>
                    Create Your First Post
                </a>
            </div>
        {% endif %}
    </div>
</div>

<style>
.post-management {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.edit-btn, .delete-btn {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.5rem 0.75rem;
    border: 1px solid;
    border-radius: 6px;
    text-decoration: none;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.edit-btn {
    background: rgba(40, 167, 69, 0.1);
    border-color: #28a745;
    color: #28a745;
}

.edit-btn:hover {
    background: #28a745;
    color: white;
}

.delete-btn {
    background: rgba(220, 53, 69, 0.1);
    border-color: #dc3545;
    color: #dc3545;
}

.delete-btn:hover {
    background: #dc3545;
    color: white;
}

.edited-note {
    display: block;
    color: #999;
    font-size: 0.8rem;
    margin-top: 0.25rem;
}

@media (max-width: 768px) {
    .post-footer {
        flex-direction: column;
        gap: 1rem;
    }
    
    .post-management {
        justify-content: center;
        flex-wrap: wrap;
    }
}
</style>

<script>
function confirmDelete(postId, postTitle) {
    if (confirm(`Are you sure you want to delete "${postTitle}"? This action cannot be undone.`)) {
        window.location.href = `/forum/post/${postId}/delete`;
    }
}
</script>
{% endblock %}
